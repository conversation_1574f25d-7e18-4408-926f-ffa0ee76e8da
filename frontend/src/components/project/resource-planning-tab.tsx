'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { resourcePlansApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { ResourcePlanForm } from '@/components/forms/resource-plan-form';
import { ResourceMatcher } from '@/components/resource-matching/resource-matcher';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Users, 
  Calendar,
  DollarSign,
  Target,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import toast from 'react-hot-toast';

interface ResourcePlanningTabProps {
  project: any;
}

export function ResourcePlanningTab({ project }: ResourcePlanningTabProps) {
  const { hasRole } = useAuth();
  const queryClient = useQueryClient();
  
  const [showPlanForm, setShowPlanForm] = useState(false);
  const [showMatcher, setShowMatcher] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<any>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');

  // Fetch resource plans for this project
  const { data: resourcePlansData, isLoading } = useQuery({
    queryKey: ['resource-plans', project.id],
    queryFn: () => resourcePlansApi.getAll({ projectId: project.id, limit: 100 }),
  });

  const resourcePlans = resourcePlansData?.data || [];

  const deleteMutation = useMutation({
    mutationFn: (id: string) => resourcePlansApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['resource-plans'] });
      toast.success('Resource plan deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to delete resource plan');
    },
  });

  const handleCreatePlan = () => {
    setSelectedPlan(null);
    setFormMode('create');
    setShowPlanForm(true);
  };

  const handleEditPlan = (plan: any) => {
    setSelectedPlan(plan);
    setFormMode('edit');
    setShowPlanForm(true);
  };

  const handleDeletePlan = (plan: any) => {
    if (confirm('Are you sure you want to delete this resource plan?')) {
      deleteMutation.mutate(plan.id);
    }
  };

  const handleMatchResources = (plan: any) => {
    setSelectedPlan(plan);
    setShowMatcher(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'bg-gray-100 text-gray-800';
      case 'ACTIVE': return 'bg-blue-100 text-blue-800';
      case 'FULFILLED': return 'bg-green-100 text-green-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'DRAFT': return <Clock className="h-4 w-4" />;
      case 'ACTIVE': return <Target className="h-4 w-4" />;
      case 'FULFILLED': return <CheckCircle className="h-4 w-4" />;
      case 'CANCELLED': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const calculatePlanProgress = (plan: any) => {
    const allocatedCount = plan.planAllocations?.length || 0;
    const requiredCount = plan.requiredCount || 1;
    return Math.min(100, (allocatedCount / requiredCount) * 100);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Resource Planning</h3>
          <p className="text-gray-600">Plan and allocate resources for project execution</p>
        </div>
        {hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
          <Button
            onClick={handleCreatePlan}
            className="bg-red-600 hover:bg-red-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Resource Plan
          </Button>
        )}
      </div>

      {/* Workflow Information */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <Target className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">Resource Planning Workflow</h4>
              <p className="text-sm text-blue-700 mt-1">
                1. Create Resource Plans → 2. Match Available Resources → 3. Allocate or Request Hiring
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Resource Plans */}
      {resourcePlans.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Resource Plans</h3>
            <p className="text-gray-600 mb-4">
              Start by creating resource plans to define the skills and roles needed for this project.
              Once created, you can match and allocate resources to each role.
            </p>
            {hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
              <Button
                onClick={handleCreatePlan}
                className="bg-red-600 hover:bg-red-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create First Resource Plan
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {resourcePlans.map((plan: any) => {
            const progress = calculatePlanProgress(plan);
            const isFullyAllocated = progress >= 100;
            
            return (
              <Card key={plan.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <CardTitle className="text-lg">{plan.role}</CardTitle>
                        <Badge className={getStatusColor(plan.status)}>
                          {getStatusIcon(plan.status)}
                          <span className="ml-1">{plan.status}</span>
                        </Badge>
                      </div>
                      <p className="text-gray-600">
                        {plan.skill?.name} • {plan.allocationPercent}% allocation • {plan.requiredCount} resource(s) needed
                      </p>
                    </div>
                    {hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleMatchResources(plan)}
                          disabled={plan.status === 'CANCELLED'}
                        >
                          <Search className="h-4 w-4 mr-1" />
                          Match
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditPlan(plan)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeletePlan(plan)}
                          disabled={plan.planAllocations?.length > 0}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Plan Details */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="font-medium">Duration</p>
                          <p className="text-gray-600">
                            {new Date(plan.startDate).toLocaleDateString()} - {new Date(plan.endDate).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Target className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="font-medium">Experience</p>
                          <p className="text-gray-600">{plan.minExperience}+ years</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="font-medium">Max Budget</p>
                          <p className="text-gray-600">
                            {plan.maxBudget ? `$${plan.maxBudget}/hour` : 'No limit'}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="font-medium">Progress</p>
                          <p className="text-gray-600">
                            {plan.planAllocations?.length || 0}/{plan.requiredCount} allocated
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Allocation Progress</span>
                        <span>{Math.round(progress)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all ${
                            isFullyAllocated ? 'bg-green-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    </div>

                    {/* Allocated Resources */}
                    {plan.planAllocations && plan.planAllocations.length > 0 && (
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Allocated Resources</h4>
                        <div className="grid gap-2">
                          {plan.planAllocations.map((allocation: any) => (
                            <div
                              key={allocation.id}
                              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                            >
                              <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                  <span className="text-xs font-medium text-red-600">
                                    {allocation.resource.user.firstName[0]}{allocation.resource.user.lastName[0]}
                                  </span>
                                </div>
                                <div>
                                  <p className="font-medium text-sm">
                                    {allocation.resource.user.firstName} {allocation.resource.user.lastName}
                                  </p>
                                  <p className="text-xs text-gray-600">
                                    {allocation.allocationPercent}% • {allocation.resource.designation}
                                  </p>
                                </div>
                              </div>
                              <Badge variant={allocation.isConfirmed ? 'default' : 'secondary'}>
                                {allocation.isConfirmed ? 'Confirmed' : 'Pending'}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Resource Requests */}
                    {plan.resourceRequests && plan.resourceRequests.length > 0 && (
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Active Resource Requests</h4>
                        <div className="text-sm text-gray-600">
                          {plan.resourceRequests.length} open request(s) for additional resources
                        </div>
                      </div>
                    )}

                    {/* Description */}
                    {plan.description && (
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Description</h4>
                        <p className="text-sm text-gray-600">{plan.description}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Modals */}
      <ResourcePlanForm
        isOpen={showPlanForm}
        onClose={() => setShowPlanForm(false)}
        resourcePlan={selectedPlan}
        projectId={project.id}
        mode={formMode}
      />

      {selectedPlan && (
        <ResourceMatcher
          isOpen={showMatcher}
          onClose={() => setShowMatcher(false)}
          resourcePlan={selectedPlan}
        />
      )}
    </div>
  );
}
