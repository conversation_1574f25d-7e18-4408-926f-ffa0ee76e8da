'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useAuth } from '@/hooks/useAuth';
import { UserRole } from '@agentic-talent-pro/shared';
import {
  LayoutDashboard,
  FileText,
  FolderOpen,
  Users,
  UserPlus,
  CheckSquare,
  Clock,
  Receipt,
  Building,
  Settings,
  LogOut,
} from 'lucide-react';

interface MenuItem {
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  roles: UserRole[];
}

const menuItems: MenuItem[] = [
  {
    label: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
    roles: [UserRole.ADMIN, UserRole.PROJECT_MANAGER, UserRole.HR_MANAGER, UserRole.BILLING_MANAGER, UserRole.RESOURCE],
  },
  {
    label: 'Contracts',
    href: '/contracts',
    icon: FileText,
    roles: [UserRole.ADMIN, UserRole.PROJECT_MANAGER, UserRole.CLIENT],
  },
  {
    label: 'Projects',
    href: '/projects',
    icon: FolderOpen,
    roles: [UserRole.ADMIN, UserRole.PROJECT_MANAGER, UserRole.RESOURCE],
  },
  {
    label: 'Resources',
    href: '/resources',
    icon: Users,
    roles: [UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.PROJECT_MANAGER],
  },
  {
    label: 'Resource Requests',
    href: '/resource-requests',
    icon: UserPlus,
    roles: [UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.PROJECT_MANAGER],
  },
  {
    label: 'Tasks',
    href: '/tasks',
    icon: CheckSquare,
    roles: [UserRole.ADMIN, UserRole.PROJECT_MANAGER, UserRole.RESOURCE],
  },
  {
    label: 'Timesheets',
    href: '/timesheets',
    icon: Clock,
    roles: [UserRole.ADMIN, UserRole.PROJECT_MANAGER, UserRole.RESOURCE, UserRole.BILLING_MANAGER],
  },
  {
    label: 'Invoices',
    href: '/invoices',
    icon: Receipt,
    roles: [UserRole.ADMIN, UserRole.BILLING_MANAGER, UserRole.CLIENT],
  },
  {
    label: 'Vendors',
    href: '/vendors',
    icon: Building,
    roles: [UserRole.ADMIN, UserRole.HR_MANAGER],
  },
];

export function Sidebar() {
  const pathname = usePathname();
  const { user, logout, hasRole } = useAuth();

  if (!user) return null;

  const filteredMenuItems = menuItems.filter(item => hasRole(item.roles));

  return (
    <div className="flex h-full w-64 flex-col redwood-sidebar">
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b border-neutral-200 bg-red-600">
        <h1 className="text-xl font-bold text-white">Agentic Talent Pro</h1>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 px-3 py-4">
        {filteredMenuItems.map((item) => {
          const Icon = item.icon;
          const isActive = pathname === item.href || pathname.startsWith(item.href + '/');

          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'redwood-nav-item',
                isActive
                  ? 'redwood-nav-item-active'
                  : 'redwood-nav-item-inactive'
              )}
            >
              <Icon className="mr-3 h-5 w-5" />
              {item.label}
            </Link>
          );
        })}
      </nav>

      {/* User section */}
      <div className="border-t border-neutral-200 p-4">
        <div className="flex items-center space-x-3 mb-3">
          <div className="h-8 w-8 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium">
            {user?.firstName?.[0] || 'U'}{user?.lastName?.[0] || 'U'}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">
              {user?.firstName || 'Unknown'} {user?.lastName || 'User'}
            </p>
            <p className="text-xs text-muted-foreground truncate">
              {user?.role?.replace('_', ' ').toLowerCase() || 'user'}
            </p>
          </div>
        </div>
        
        <div className="space-y-1">
          <Link
            href="/settings"
            className="redwood-nav-item redwood-nav-item-inactive"
          >
            <Settings className="mr-3 h-4 w-4" />
            Settings
          </Link>

          <button
            onClick={logout}
            className="redwood-nav-item redwood-nav-item-inactive w-full"
          >
            <LogOut className="mr-3 h-4 w-4" />
            Logout
          </button>
        </div>
      </div>
    </div>
  );
}
