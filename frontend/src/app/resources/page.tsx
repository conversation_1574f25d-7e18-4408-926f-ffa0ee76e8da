'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { resourcesApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { getStatusColor, getInitials } from '@/lib/utils';
import { Plus, Search, Filter, Eye, Edit, Mail, Phone, MapPin, Award } from 'lucide-react';

export default function ResourcesPage() {
  const { hasRole } = useAuth();
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState('');

  const { data: resourcesData, isLoading } = useQuery({
    queryKey: ['resources', page, status],
    queryFn: () => resourcesApi.getAll({ page, limit: 12, status: status || undefined }),
  });

  const resources = resourcesData?.data || [];
  const pagination = resourcesData?.pagination;

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="spinner"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Resources</h1>
            <p className="text-gray-600">Manage team members and contractors</p>
          </div>
          {hasRole(['ADMIN', 'HR_MANAGER']) && (
            <Button className="bg-red-600 hover:bg-red-700">
              <Plus className="h-4 w-4 mr-2" />
              Add Resource
            </Button>
          )}
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search resources..."
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  />
                </div>
              </div>
              <select
                value={status}
                onChange={(e) => setStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">All Status</option>
                <option value="AVAILABLE">Available</option>
                <option value="ALLOCATED">Allocated</option>
                <option value="ON_LEAVE">On Leave</option>
                <option value="TERMINATED">Terminated</option>
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Resources Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {resources.map((resource: any) => (
            <Card key={resource.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-3">
                  <span className="text-xl font-semibold text-red-600">
                    {getInitials(resource.user?.firstName || 'N', resource.user?.lastName || 'A')}
                  </span>
                </div>
                <CardTitle className="text-lg text-gray-900">
                  {resource.user?.firstName} {resource.user?.lastName}
                </CardTitle>
                <CardDescription>
                  {resource.designation} • {resource.department}
                </CardDescription>
                <Badge className={getStatusColor(resource.status)}>
                  {resource.status}
                </Badge>
              </CardHeader>
              <CardContent>
                {/* Contact Info */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <Mail className="h-4 w-4 mr-2" />
                    <span className="truncate">{resource.user?.email}</span>
                  </div>
                  {resource.user?.phone && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Phone className="h-4 w-4 mr-2" />
                      <span>{resource.user.phone}</span>
                    </div>
                  )}
                  {resource.location && (
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      <span>{resource.location}</span>
                    </div>
                  )}
                </div>

                {/* Employment Details */}
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Employee ID:</span>
                    <span className="font-medium">{resource.employeeId}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Type:</span>
                    <span className="font-medium">{resource.employmentType?.replace('_', ' ')}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Rate:</span>
                    <span className="font-medium">${resource.hourlyRate}/hr</span>
                  </div>
                </div>

                {/* Skills */}
                {resource.skills && resource.skills.length > 0 && (
                  <div className="mb-4">
                    <div className="flex items-center text-sm text-gray-600 mb-2">
                      <Award className="h-4 w-4 mr-1" />
                      <span>Skills</span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {resource.skills.slice(0, 3).map((skill: any, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {skill.skill?.name}
                        </Badge>
                      ))}
                      {resource.skills.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{resource.skills.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  {hasRole(['ADMIN', 'HR_MANAGER']) && (
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-700">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} results
            </p>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                onClick={() => setPage(page + 1)}
                disabled={page === pagination.totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}

        {resources.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <p className="text-gray-500 mb-4">No resources found</p>
              {hasRole(['ADMIN', 'HR_MANAGER']) && (
                <Button className="bg-red-600 hover:bg-red-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Resource
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}
