'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { resourceRequestsApi, usersApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { getStatusColor } from '@/lib/utils';
import { 
  FileText, 
  Calendar, 
  User, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  UserCheck,
  Filter,
  Search
} from 'lucide-react';
import toast from 'react-hot-toast';

export default function ResourceRequestsPage() {
  const { hasRole, user } = useAuth();
  const queryClient = useQueryClient();
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState('');
  const [priority, setPriority] = useState('');

  const { data: resourceRequestsData, isLoading } = useQuery({
    queryKey: ['resource-requests', page, status, priority],
    queryFn: () => resourceRequestsApi.getAll({ 
      page, 
      limit: 10, 
      status: status || undefined,
      priority: priority || undefined 
    }),
  });

  const { data: hrManagersData } = useQuery({
    queryKey: ['hr-managers'],
    queryFn: () => usersApi.getAll({ role: 'HR_MANAGER', limit: 100 }),
    enabled: hasRole(['ADMIN']),
  });

  const resourceRequests = resourceRequestsData?.data || [];
  const pagination = resourceRequestsData?.pagination;
  const hrManagers = hrManagersData?.data || [];

  const assignMutation = useMutation({
    mutationFn: ({ id, assignedTo }: { id: string; assignedTo: string }) => 
      resourceRequestsApi.assign(id, { assignedTo }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['resource-requests'] });
      toast.success('Resource request assigned successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to assign resource request');
    },
  });

  const fulfillMutation = useMutation({
    mutationFn: ({ id, notes }: { id: string; notes?: string }) => 
      resourceRequestsApi.fulfill(id, { notes }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['resource-requests'] });
      toast.success('Resource request fulfilled successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to fulfill resource request');
    },
  });

  const handleAssign = (requestId: string, assignedTo: string) => {
    assignMutation.mutate({ id: requestId, assignedTo });
  };

  const handleFulfill = (requestId: string) => {
    const notes = prompt('Add fulfillment notes (optional):');
    fulfillMutation.mutate({ id: requestId, notes: notes || undefined });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'OPEN': return <AlertCircle className="h-4 w-4" />;
      case 'IN_PROGRESS': return <Clock className="h-4 w-4" />;
      case 'FULFILLED': return <CheckCircle className="h-4 w-4" />;
      case 'CANCELLED': return <AlertCircle className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW': return 'bg-gray-100 text-gray-800';
      case 'MEDIUM': return 'bg-blue-100 text-blue-800';
      case 'HIGH': return 'bg-orange-100 text-orange-800';
      case 'URGENT': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="spinner"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Resource Requests</h1>
            <p className="text-gray-600">Manage and fulfill resource hiring requests</p>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search requests..."
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  />
                </div>
              </div>
              <select
                value={status}
                onChange={(e) => setStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">All Status</option>
                <option value="OPEN">Open</option>
                <option value="IN_PROGRESS">In Progress</option>
                <option value="FULFILLED">Fulfilled</option>
                <option value="CANCELLED">Cancelled</option>
              </select>
              <select
                value={priority}
                onChange={(e) => setPriority(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">All Priority</option>
                <option value="LOW">Low</option>
                <option value="MEDIUM">Medium</option>
                <option value="HIGH">High</option>
                <option value="URGENT">Urgent</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Resource Requests */}
        {resourceRequests.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Resource Requests</h3>
              <p className="text-gray-600">
                No resource requests found matching your criteria.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {resourceRequests.map((request: any) => (
              <Card key={request.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <CardTitle className="text-lg">{request.title}</CardTitle>
                        <Badge className={getStatusColor(request.status)}>
                          {getStatusIcon(request.status)}
                          <span className="ml-1">{request.status}</span>
                        </Badge>
                        <Badge className={getPriorityColor(request.priority)}>
                          {request.priority}
                        </Badge>
                      </div>
                      <p className="text-gray-600 mb-2">{request.description}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>Project: {request.resourcePlan?.project?.name}</span>
                        <span>Role: {request.resourcePlan?.role}</span>
                        <span>Skill: {request.resourcePlan?.skill?.name}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      {hasRole(['ADMIN']) && request.status === 'OPEN' && (
                        <select
                          onChange={(e) => {
                            if (e.target.value) {
                              handleAssign(request.id, e.target.value);
                            }
                          }}
                          className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                          defaultValue=""
                        >
                          <option value="">Assign to HR</option>
                          {hrManagers.map((manager: any) => (
                            <option key={manager.id} value={manager.id}>
                              {manager.firstName} {manager.lastName}
                            </option>
                          ))}
                        </select>
                      )}
                      {hasRole(['ADMIN', 'HR_MANAGER']) && 
                       request.status === 'IN_PROGRESS' && 
                       (hasRole(['ADMIN']) || request.assignedTo === user?.id) && (
                        <Button
                          size="sm"
                          onClick={() => handleFulfill(request.id)}
                          className="bg-green-600 hover:bg-green-700"
                          disabled={fulfillMutation.isPending}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Fulfill
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Request Details */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="font-medium">Requested by</p>
                          <p className="text-gray-600">
                            {request.requester?.firstName} {request.requester?.lastName}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <UserCheck className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="font-medium">Assigned to</p>
                          <p className="text-gray-600">
                            {request.assignee 
                              ? `${request.assignee.firstName} ${request.assignee.lastName}`
                              : 'Unassigned'
                            }
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="font-medium">Expected Date</p>
                          <p className="text-gray-600">
                            {request.expectedDate 
                              ? new Date(request.expectedDate).toLocaleDateString()
                              : 'Not specified'
                            }
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="font-medium">Created</p>
                          <p className="text-gray-600">
                            {new Date(request.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Job Description */}
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Job Description</h4>
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <pre className="text-sm text-gray-700 whitespace-pre-wrap font-sans">
                          {request.jobDescription}
                        </pre>
                      </div>
                    </div>

                    {/* Required Skills */}
                    {request.requiredSkills && (
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Required Skills</h4>
                        <div className="flex flex-wrap gap-2">
                          {JSON.parse(request.requiredSkills).map((skill: any, index: number) => (
                            <Badge key={index} variant="outline">
                              {skill.skillName || skill.name}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Requirements */}
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Min Experience:</span>
                        <span className="ml-2">{request.minExperience} years</span>
                      </div>
                      <div>
                        <span className="font-medium">Max Budget:</span>
                        <span className="ml-2">
                          {request.maxBudget ? `$${request.maxBudget}/hour` : 'No limit'}
                        </span>
                      </div>
                      {request.fulfilledDate && (
                        <div>
                          <span className="font-medium">Fulfilled:</span>
                          <span className="ml-2">
                            {new Date(request.fulfilledDate).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-700">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} results
            </p>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => setPage(page - 1)}
                disabled={page <= 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                onClick={() => setPage(page + 1)}
                disabled={page >= pagination.totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
