'use client';

import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { dashboardApi } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { UserRole } from '@agentic-talent-pro/shared';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from 'recharts';
import {
  FileText,
  FolderOpen,
  Users,
  Clock,
  Receipt,
  AlertCircle,
  TrendingUp,
  CheckCircle,
} from 'lucide-react';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default function DashboardPage() {
  const { user, hasRole } = useAuth();

  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: dashboardApi.getStats,
  });

  const { data: charts, isLoading: chartsLoading } = useQuery({
    queryKey: ['dashboard-charts'],
    queryFn: dashboardApi.getCharts,
  });

  const isLoading = statsLoading || chartsLoading;

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="spinner"></div>
        </div>
      </DashboardLayout>
    );
  }

  const statsData = (stats as any)?.data || stats || {};
  const chartsData = (charts as any)?.data || charts || {};

  const overview = statsData?.overview || {};
  const projectStatus = chartsData?.projectStatus || [];
  const resourceUtilization = chartsData?.resourceUtilization || [];
  const monthlyInvoices = chartsData?.monthlyInvoices || [];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {user?.firstName}!
          </h1>
          <p className="text-gray-600">
            Here's what's happening with your projects today.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {hasRole([UserRole.ADMIN, UserRole.PROJECT_MANAGER]) && (
            <>
              <Card className="redwood-card">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-gray-700">
                    Total Contracts
                  </CardTitle>
                  <FileText className="h-4 w-4 text-red-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-gray-900">{overview.totalContracts || 0}</div>
                  <p className="text-xs text-gray-500">
                    Active agreements
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Active Projects
                  </CardTitle>
                  <FolderOpen className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{overview.activeProjects || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    Currently running
                  </p>
                </CardContent>
              </Card>
            </>
          )}

          {hasRole([UserRole.ADMIN, UserRole.HR_MANAGER]) && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Available Resources
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{overview.totalResources || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Ready for assignment
                </p>
              </CardContent>
            </Card>
          )}

          {hasRole([UserRole.ADMIN, UserRole.PROJECT_MANAGER, UserRole.BILLING_MANAGER]) && (
            <>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Pending Timesheets
                  </CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{overview.pendingTimesheets || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    Awaiting approval
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Unpaid Invoices
                  </CardTitle>
                  <Receipt className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{overview.unpaidInvoices || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    Require attention
                  </p>
                </CardContent>
              </Card>
            </>
          )}

          {/* Resource-specific stats */}
          {user?.role === UserRole.RESOURCE && stats?.myTasks !== undefined && (
            <>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    My Tasks
                  </CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.myTasks}</div>
                  <p className="text-xs text-muted-foreground">
                    Assigned to me
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    My Timesheets
                  </CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.myTimesheets}</div>
                  <p className="text-xs text-muted-foreground">
                    Total submitted
                  </p>
                </CardContent>
              </Card>
            </>
          )}

          {/* Project Manager specific stats */}
          {user?.role === UserRole.PROJECT_MANAGER && stats?.myProjects !== undefined && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  My Projects
                </CardTitle>
                <FolderOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.myProjects}</div>
                <p className="text-xs text-muted-foreground">
                  Under my management
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Charts */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {/* Project Status Chart */}
          {hasRole([UserRole.ADMIN, UserRole.PROJECT_MANAGER]) && projectStatus.length > 0 && (
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Project Status</CardTitle>
                <CardDescription>
                  Distribution of project statuses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={projectStatus}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ status, count }) => `${status}: ${count}`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {projectStatus.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          )}

          {/* Resource Utilization Chart */}
          {hasRole([UserRole.ADMIN, UserRole.HR_MANAGER]) && resourceUtilization.length > 0 && (
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Resource Utilization</CardTitle>
                <CardDescription>
                  Current resource allocation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={resourceUtilization}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="status" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          )}

          {/* Monthly Invoice Trends */}
          {hasRole([UserRole.ADMIN, UserRole.BILLING_MANAGER]) && monthlyInvoices.length > 0 && (
            <Card className="col-span-1 lg:col-span-1">
              <CardHeader>
                <CardTitle>Invoice Trends</CardTitle>
                <CardDescription>
                  Monthly invoice totals (last 6 months)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={monthlyInvoices}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`$${value}`, 'Total']} />
                    <Line type="monotone" dataKey="total" stroke="#8884d8" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks you might want to perform
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {hasRole([UserRole.ADMIN, UserRole.PROJECT_MANAGER]) && (
                <>
                  <a
                    href="/contracts"
                    className="flex items-center p-4 border rounded-lg hover:bg-accent transition-colors"
                  >
                    <FileText className="h-8 w-8 text-primary mr-3" />
                    <div>
                      <h3 className="font-medium">Manage Contracts</h3>
                      <p className="text-sm text-muted-foreground">View and create contracts</p>
                    </div>
                  </a>
                  
                  <a
                    href="/projects"
                    className="flex items-center p-4 border rounded-lg hover:bg-accent transition-colors"
                  >
                    <FolderOpen className="h-8 w-8 text-primary mr-3" />
                    <div>
                      <h3 className="font-medium">Manage Projects</h3>
                      <p className="text-sm text-muted-foreground">Create and track projects</p>
                    </div>
                  </a>
                </>
              )}

              {hasRole([UserRole.ADMIN, UserRole.HR_MANAGER]) && (
                <a
                  href="/resources"
                  className="flex items-center p-4 border rounded-lg hover:bg-accent transition-colors"
                >
                  <Users className="h-8 w-8 text-primary mr-3" />
                  <div>
                    <h3 className="font-medium">Manage Resources</h3>
                    <p className="text-sm text-muted-foreground">View and assign resources</p>
                  </div>
                </a>
              )}

              <a
                href="/timesheets"
                className="flex items-center p-4 border rounded-lg hover:bg-accent transition-colors"
              >
                <Clock className="h-8 w-8 text-primary mr-3" />
                <div>
                  <h3 className="font-medium">Timesheets</h3>
                  <p className="text-sm text-muted-foreground">
                    {user?.role === UserRole.RESOURCE ? 'Submit timesheets' : 'Review timesheets'}
                  </p>
                </div>
              </a>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
