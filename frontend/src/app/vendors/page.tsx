'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuth';
import { getInitials } from '@/lib/utils';
import { Plus, Search, Filter, Eye, Edit, Mail, Phone, MapPin, Building, Star } from 'lucide-react';

export default function VendorsPage() {
  const { hasRole } = useAuth();

  // Mock data for demonstration
  const vendors = [
    {
      id: 1,
      name: 'TechCorp Solutions',
      contactPerson: '<PERSON>',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Tech Street, San Francisco, CA 94105',
      category: 'SOFTWARE_DEVELOPMENT',
      status: 'ACTIVE',
      rating: 4.8,
      contractsCount: 5,
      totalValue: 250000,
      description: 'Leading software development company specializing in enterprise solutions',
    },
    {
      id: 2,
      name: 'Design Studio Pro',
      contact<PERSON><PERSON>: '<PERSON>',
      email: '<EMAIL>',
      phone: '******-0456',
      address: '456 Creative Ave, New York, NY 10001',
      category: 'DESIGN',
      status: 'ACTIVE',
      rating: 4.9,
      contractsCount: 3,
      totalValue: 75000,
      description: 'Creative design studio for UI/UX and branding projects',
    },
    {
      id: 3,
      name: 'CloudOps Consulting',
      contactPerson: 'Alex Rodriguez',
      email: '<EMAIL>',
      phone: '******-0789',
      address: '789 Cloud Lane, Austin, TX 78701',
      category: 'CONSULTING',
      status: 'PENDING',
      rating: 4.5,
      contractsCount: 1,
      totalValue: 50000,
      description: 'Cloud infrastructure and DevOps consulting services',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'status-active';
      case 'PENDING': return 'status-pending';
      case 'INACTIVE': return 'status-inactive';
      case 'SUSPENDED': return 'status-cancelled';
      default: return 'status-inactive';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'SOFTWARE_DEVELOPMENT': return 'bg-blue-100 text-blue-800';
      case 'DESIGN': return 'bg-purple-100 text-purple-800';
      case 'CONSULTING': return 'bg-green-100 text-green-800';
      case 'MARKETING': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Vendors</h1>
            <p className="text-gray-600">Manage vendor relationships and contracts</p>
          </div>
          {hasRole(['ADMIN', 'HR_MANAGER']) && (
            <Button className="redwood-button-primary">
              <Plus className="h-4 w-4 mr-2" />
              Add Vendor
            </Button>
          )}
        </div>

        {/* Filters */}
        <Card className="redwood-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search vendors..."
                    className="redwood-input pl-10 w-full"
                  />
                </div>
              </div>
              <select className="redwood-input">
                <option value="">All Categories</option>
                <option value="SOFTWARE_DEVELOPMENT">Software Development</option>
                <option value="DESIGN">Design</option>
                <option value="CONSULTING">Consulting</option>
                <option value="MARKETING">Marketing</option>
              </select>
              <select className="redwood-input">
                <option value="">All Status</option>
                <option value="ACTIVE">Active</option>
                <option value="PENDING">Pending</option>
                <option value="INACTIVE">Inactive</option>
                <option value="SUSPENDED">Suspended</option>
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Vendors Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {vendors.map((vendor) => (
            <Card key={vendor.id} className="redwood-card">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                      <Building className="h-6 w-6 text-red-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg text-gray-900">{vendor.name}</CardTitle>
                      <CardDescription>{vendor.contactPerson}</CardDescription>
                    </div>
                  </div>
                  <Badge className={getStatusColor(vendor.status)}>
                    {vendor.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {vendor.description}
                </p>

                {/* Category */}
                <div className="mb-4">
                  <Badge className={getCategoryColor(vendor.category)}>
                    {vendor.category.replace('_', ' ')}
                  </Badge>
                </div>

                {/* Contact Info */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <Mail className="h-4 w-4 mr-2" />
                    <span className="truncate">{vendor.email}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Phone className="h-4 w-4 mr-2" />
                    <span>{vendor.phone}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="h-4 w-4 mr-2" />
                    <span className="truncate">{vendor.address}</span>
                  </div>
                </div>

                {/* Rating */}
                <div className="flex items-center mb-4">
                  <div className="flex items-center mr-2">
                    {renderStars(vendor.rating)}
                  </div>
                  <span className="text-sm font-medium text-gray-700">{vendor.rating}</span>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div>
                    <span className="text-gray-600">Contracts:</span>
                    <span className="ml-1 font-medium">{vendor.contractsCount}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Total Value:</span>
                    <span className="ml-1 font-medium">${vendor.totalValue.toLocaleString()}</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  {hasRole(['ADMIN', 'HR_MANAGER']) && (
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {vendors.length === 0 && (
          <Card className="redwood-card">
            <CardContent className="text-center py-12">
              <p className="text-gray-500 mb-4">No vendors found</p>
              {hasRole(['ADMIN', 'HR_MANAGER']) && (
                <Button className="redwood-button-primary">
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Vendor
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}
