'use client';

import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { apiRequest } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { 
  ArrowLeft, 
  User, 
  Briefcase, 
  GraduationCap, 
  Award, 
  Languages, 
  Calendar,
  MapPin,
  Phone,
  Mail,
  Building,
  DollarSign,
  Shield,
  FileText,
  Clock,
  TrendingUp,
  Users,
  Target
} from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function WorkforceDetailPage() {
  const { hasRole } = useAuth();
  const params = useParams();
  const id = params.id as string;

  const { data: workforceData, isLoading } = useQuery({
    queryKey: ['workforce', id],
    queryFn: () => apiRequest.get(`/workforce/${id}`),
  });

  const member = workforceData?.data;

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="spinner"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!member) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">Workforce Member Not Found</h2>
          <p className="text-gray-600 mt-2">The requested workforce member could not be found.</p>
          <Link href="/workforce">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Workforce
            </Button>
          </Link>
        </div>
      </DashboardLayout>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE': return 'bg-green-100 text-green-800';
      case 'ALLOCATED': return 'bg-blue-100 text-blue-800';
      case 'ON_LEAVE': return 'bg-yellow-100 text-yellow-800';
      case 'TERMINATED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/workforce">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <span className="text-2xl font-bold text-red-600">
                {member.firstName?.[0]}{member.lastName?.[0]}
              </span>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {member.firstName} {member.lastName}
              </h1>
              <p className="text-gray-600">{member.designation} • {member.department}</p>
              <div className="flex items-center space-x-2 mt-1">
                <Badge className={getStatusColor(member.status)}>
                  {member.status}
                </Badge>
                <Badge variant="outline">
                  {member.employmentType}
                </Badge>
              </div>
            </div>
          </div>
          {hasRole(['ADMIN', 'HR_MANAGER']) && (
            <Button className="bg-red-600 hover:bg-red-700">
              <User className="h-4 w-4 mr-2" />
              Edit Profile
            </Button>
          )}
        </div>

        {/* Utilization Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Current Allocation</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {member.utilizationMetrics?.currentAllocation || 0}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Available Capacity</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {member.utilizationMetrics?.availableCapacity || 100}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Briefcase className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active Projects</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {member.utilizationMetrics?.activeProjects || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Timesheets</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {member.utilizationMetrics?.totalTimesheets || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Information Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="skills">Skills</TabsTrigger>
            <TabsTrigger value="projects">Projects</TabsTrigger>
            <TabsTrigger value="education">Education</TabsTrigger>
            <TabsTrigger value="compliance">Compliance</TabsTrigger>
            <TabsTrigger value="timesheets">Timesheets</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Personal Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <User className="h-5 w-5" />
                    <span>Personal Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-600">Employee ID:</span>
                      <p>{member.employeeId}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Email:</span>
                      <p>{member.email}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Phone:</span>
                      <p>{member.phone || 'Not provided'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Location:</span>
                      <p>{member.location}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Joining Date:</span>
                      <p>{new Date(member.joiningDate).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Hourly Rate:</span>
                      <p>${member.hourlyRate}/hour</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Professional Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Briefcase className="h-5 w-5" />
                    <span>Professional Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-gray-600">Department:</span>
                      <p>{member.department}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Designation:</span>
                      <p>{member.designation}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Employment Type:</span>
                      <p>{member.employmentType}</p>
                    </div>
                    {member.vendor_name && (
                      <div>
                        <span className="font-medium text-gray-600">Vendor:</span>
                        <p>{member.vendor_name}</p>
                      </div>
                    )}
                    {member.totalExperience && (
                      <div>
                        <span className="font-medium text-gray-600">Total Experience:</span>
                        <p>{member.totalExperience} years</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Skills Tab */}
          <TabsContent value="skills">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Award className="h-5 w-5" />
                  <span>Skills & Expertise</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {member.skills && member.skills.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {member.skills.map((skill: any) => (
                      <div key={skill.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{skill.skill_name}</h4>
                          <Badge variant="outline">{skill.category}</Badge>
                        </div>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Proficiency:</span>
                            <span className="font-medium">{skill.proficiencyLevel}/5</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Experience:</span>
                            <span>{skill.yearsOfExperience} years</span>
                          </div>
                          {skill.certified && (
                            <div className="flex items-center space-x-1 text-green-600">
                              <Award className="h-4 w-4" />
                              <span>Certified</span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">No skills information available</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Projects Tab */}
          <TabsContent value="projects">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Briefcase className="h-5 w-5" />
                  <span>Project Allocations</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {member.projects && member.projects.length > 0 ? (
                  <div className="space-y-4">
                    {member.projects.map((project: any) => (
                      <div key={project.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{project.project_name}</h4>
                          <Badge className={project.project_status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                            {project.project_status}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-600">Allocation:</span>
                            <p>{project.allocationPercent}%</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">Start Date:</span>
                            <p>{new Date(project.startDate).toLocaleDateString()}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-600">End Date:</span>
                            <p>{project.endDate ? new Date(project.endDate).toLocaleDateString() : 'Ongoing'}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">No project allocations found</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Education Tab */}
          <TabsContent value="education">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <GraduationCap className="h-5 w-5" />
                  <span>Education & Certifications</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500 text-center py-8">Education and certification details will be displayed here</p>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Compliance Tab */}
          <TabsContent value="compliance">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-5 w-5" />
                  <span>Compliance & Security</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium">Background Verification</h4>
                    <div className="flex items-center space-x-2">
                      <Shield className={`h-5 w-5 ${member.backgroundCheck ? 'text-green-600' : 'text-gray-400'}`} />
                      <span className={member.backgroundCheck ? 'text-green-600' : 'text-gray-600'}>
                        {member.backgroundCheck ? 'Verified' : 'Pending'}
                      </span>
                    </div>
                  </div>
                  {member.securityClearance && (
                    <div className="space-y-4">
                      <h4 className="font-medium">Security Clearance</h4>
                      <Badge className="bg-blue-100 text-blue-800">
                        {member.securityClearance}
                      </Badge>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Timesheets Tab */}
          <TabsContent value="timesheets">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Clock className="h-5 w-5" />
                  <span>Recent Timesheets</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {member.recentTimesheets && member.recentTimesheets.length > 0 ? (
                  <div className="space-y-4">
                    {member.recentTimesheets.map((timesheet: any) => (
                      <div key={timesheet.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{timesheet.project_name}</h4>
                          <Badge variant="outline">
                            {timesheet.totalHours} hours
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600">
                          Week of {new Date(timesheet.weekStarting).toLocaleDateString()}
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">No timesheet records found</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
