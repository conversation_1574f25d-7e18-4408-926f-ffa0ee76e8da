'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { apiRequest } from '@/lib/api';
import { useAuth } from '@/hooks/useAuth';
import { 
  Users, 
  Search, 
  Filter, 
  Eye, 
  UserCheck, 
  Building, 
  MapPin,
  Calendar,
  Award,
  GraduationCap,
  Languages,
  Shield,
  DollarSign
} from 'lucide-react';
import Link from 'next/link';

export default function WorkforcePage() {
  const { hasRole } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [department, setDepartment] = useState('');
  const [location, setLocation] = useState('');
  const [status, setStatus] = useState('');
  const [employmentType, setEmploymentType] = useState('');

  const { data: workforceData, isLoading } = useQuery({
    queryKey: ['workforce', department, location, status, employmentType],
    queryFn: () => apiRequest.get('/workforce', { 
      department: department || undefined,
      location: location || undefined,
      status: status || undefined,
      employmentType: employmentType || undefined,
    }),
  });

  const workforce = workforceData?.data || [];

  const filteredWorkforce = workforce.filter((member: any) =>
    searchTerm === '' ||
    `${member.firstName} ${member.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.designation.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE': return 'bg-green-100 text-green-800';
      case 'ALLOCATED': return 'bg-blue-100 text-blue-800';
      case 'ON_LEAVE': return 'bg-yellow-100 text-yellow-800';
      case 'TERMINATED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getEmploymentTypeColor = (type: string) => {
    switch (type) {
      case 'FTE': return 'bg-purple-100 text-purple-800';
      case 'CONTRACTOR': return 'bg-orange-100 text-orange-800';
      case 'VENDOR': return 'bg-cyan-100 text-cyan-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="spinner"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Workforce Management</h1>
            <p className="text-gray-600">Comprehensive employee profiles and workforce analytics</p>
          </div>
          {hasRole(['ADMIN', 'HR_MANAGER']) && (
            <Button className="bg-red-600 hover:bg-red-700">
              <UserCheck className="h-4 w-4 mr-2" />
              Add New Employee
            </Button>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Workforce</p>
                  <p className="text-2xl font-bold text-gray-900">{workforce.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <UserCheck className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Available</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {workforce.filter((w: any) => w.status === 'AVAILABLE').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Building className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">FTE</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {workforce.filter((w: any) => w.employmentType === 'FTE').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Shield className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Contractors</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {workforce.filter((w: any) => w.employmentType === 'CONTRACTOR').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search workforce..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={department}
                onChange={(e) => setDepartment(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">All Departments</option>
                <option value="Engineering">Engineering</option>
                <option value="Design">Design</option>
                <option value="Marketing">Marketing</option>
                <option value="Sales">Sales</option>
                <option value="HR">HR</option>
              </select>
              <select
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">All Locations</option>
                <option value="Bangalore">Bangalore</option>
                <option value="Mumbai">Mumbai</option>
                <option value="Delhi">Delhi</option>
                <option value="Remote">Remote</option>
              </select>
              <select
                value={status}
                onChange={(e) => setStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">All Status</option>
                <option value="AVAILABLE">Available</option>
                <option value="ALLOCATED">Allocated</option>
                <option value="ON_LEAVE">On Leave</option>
                <option value="TERMINATED">Terminated</option>
              </select>
              <select
                value={employmentType}
                onChange={(e) => setEmploymentType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="">All Types</option>
                <option value="FTE">FTE</option>
                <option value="CONTRACTOR">Contractor</option>
                <option value="VENDOR">Vendor</option>
              </select>
              <Button variant="outline" onClick={() => {
                setSearchTerm('');
                setDepartment('');
                setLocation('');
                setStatus('');
                setEmploymentType('');
              }}>
                <Filter className="h-4 w-4 mr-2" />
                Clear
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Workforce List */}
        <div className="grid gap-6">
          {filteredWorkforce.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Workforce Members Found</h3>
                <p className="text-gray-600">
                  {searchTerm || department || location || status || employmentType
                    ? 'No workforce members match your search criteria.'
                    : 'No workforce members have been added yet.'
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredWorkforce.map((member: any) => (
              <Card key={member.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                        <span className="text-lg font-semibold text-red-600">
                          {member.firstName?.[0]}{member.lastName?.[0]}
                        </span>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {member.firstName} {member.lastName}
                          </h3>
                          <Badge className={getStatusColor(member.status)}>
                            {member.status}
                          </Badge>
                          <Badge className={getEmploymentTypeColor(member.employmentType)}>
                            {member.employmentType}
                          </Badge>
                        </div>
                        <p className="text-gray-600 mb-3">{member.designation} • {member.department}</p>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4 text-gray-400" />
                            <span>{member.location}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span>Joined {new Date(member.joiningDate).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <DollarSign className="h-4 w-4 text-gray-400" />
                            <span>${member.hourlyRate}/hour</span>
                          </div>
                          {member.vendor_name && (
                            <div className="flex items-center space-x-2">
                              <Building className="h-4 w-4 text-gray-400" />
                              <span>{member.vendor_name}</span>
                            </div>
                          )}
                        </div>

                        {/* Quick Info Icons */}
                        <div className="flex items-center space-x-4 mt-3">
                          {member.backgroundCheck && (
                            <div className="flex items-center space-x-1 text-green-600">
                              <Shield className="h-4 w-4" />
                              <span className="text-xs">Verified</span>
                            </div>
                          )}
                          {member.securityClearance && (
                            <div className="flex items-center space-x-1 text-blue-600">
                              <Award className="h-4 w-4" />
                              <span className="text-xs">{member.securityClearance}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Link href={`/workforce/${member.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          360 View
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
