version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: agentic-talent-pro-db
    environment:
      POSTGRES_DB: agentic_talent_pro
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - agentic-network

  # Backend API
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: agentic-talent-pro-backend
    environment:
      DATABASE_URL: ********************************************/agentic_talent_pro
      JWT_SECRET: ${JWT_SECRET:-atp_prod_jwt_2024_secure_key_f8e9d7c6b5a4938271605f4e3d2c1b0a9e8d7c6b5a4938271605f4e3d2c1b0a}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET:-atp_prod_refresh_2024_secure_key_b0a9e8d7c6b5a4938271605f4e3d2c1b0a9e8d7c6b5a4938271605f4e3d2c1}
      NODE_ENV: production
      PORT: 3001
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3002}
    ports:
      - "3001:3001"
    depends_on:
      - postgres
    networks:
      - agentic-network
    volumes:
      - ./backend/uploads:/app/uploads

  # Frontend
  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    container_name: agentic-talent-pro-frontend
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:3001/api
      NEXTAUTH_URL: http://localhost:3000
      NEXTAUTH_SECRET: your-nextauth-secret-here
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - agentic-network

  # Redis (for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: agentic-talent-pro-redis
    ports:
      - "6379:6379"
    networks:
      - agentic-network

volumes:
  postgres_data:

networks:
  agentic-network:
    driver: bridge
