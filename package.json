{"name": "agentic-talent-pro", "version": "1.0.0", "description": "Production-ready Contract Management, Project Management, HRMS, and Billing Management System", "private": true, "workspaces": ["frontend", "backend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:shared": "cd shared && npm run build", "start": "npm run start:backend", "start:backend": "cd backend && npm run start", "db:generate": "cd backend && npx prisma generate", "db:migrate": "cd backend && npx prisma migrate dev", "db:studio": "cd backend && npx prisma studio", "db:seed": "cd backend && npm run seed", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm run test", "test:frontend": "cd frontend && npm run test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}