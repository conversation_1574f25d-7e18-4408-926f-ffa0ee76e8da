# Production Deployment Guide

This guide covers the production deployment of Agentic Talent Pro enterprise application.

## 🔒 Security Checklist

### Before Deployment

- [ ] **Environment Variables**: All default secrets replaced with secure values
- [ ] **Database**: Production database configured with proper credentials
- [ ] **CORS**: Frontend domain configured (no localhost in production)
- [ ] **HTTPS**: SSL certificates configured via reverse proxy
- [ ] **Firewall**: Proper network security rules in place
- [ ] **Backups**: Database backup strategy implemented

### Security Requirements

1. **JWT Secrets**: Generate using `openssl rand -hex 64`
2. **Database**: Use strong passwords and restrict access
3. **Environment**: Set `NODE_ENV=production`
4. **File Permissions**: Restrict access to `.env` files
5. **Network**: Use HTTPS and proper CORS configuration

## 🚀 Quick Production Deployment

### Automated Deployment

```bash
# Run the production deployment script
./deploy-production.sh
```

This script will:
- Validate environment configuration
- Check for security issues
- Install dependencies
- Build all applications
- Run database migrations
- Verify production readiness

### Manual Deployment

1. **Environment Setup**
```bash
# Copy and configure environment
cp backend/.env.production.example backend/.env
# Edit backend/.env with production values
```

2. **Install Dependencies**
```bash
npm ci --only=production
```

3. **Build Applications**
```bash
npm run build
```

4. **Database Setup**
```bash
cd backend
npm run db:migrate deploy
```

5. **Start Services**
```bash
# Backend
cd backend && npm start

# Frontend (in another terminal)
cd frontend && npm start
```

## 🏗️ Production Architecture

### Recommended Infrastructure

```
[Load Balancer] → [Reverse Proxy (Nginx)] → [Application Servers]
                                          ↓
[Database (PostgreSQL)] ← [Application] → [File Storage]
                                          ↓
                                    [Monitoring & Logs]
```

### System Requirements

- **Node.js**: 18.0.0 or higher
- **PostgreSQL**: 13.0 or higher
- **Memory**: Minimum 2GB RAM
- **Storage**: Minimum 10GB available space
- **Network**: HTTPS-enabled domain

## 🔧 Configuration

### Environment Variables

Critical production environment variables:

```env
# Database
DATABASE_URL="postgresql://user:pass@host:port/db"

# Security
JWT_SECRET="64-character-hex-string"
JWT_REFRESH_SECRET="different-64-character-hex-string"
NODE_ENV=production

# Network
CORS_ORIGIN="https://your-domain.com"
PORT=3003

# Optional
LOG_LEVEL=info
RATE_LIMIT_MAX_REQUESTS=100
```

### Database Configuration

1. **Connection Pooling**: Configure appropriate pool size
2. **SSL**: Enable SSL connections in production
3. **Backups**: Set up automated daily backups
4. **Monitoring**: Monitor connection counts and performance

### Reverse Proxy (Nginx Example)

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    # API Backend
    location /api/ {
        proxy_pass http://localhost:3003;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Frontend
    location / {
        proxy_pass http://localhost:3002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📊 Monitoring & Maintenance

### Health Checks

- **Endpoint**: `GET /health`
- **Expected Response**: `{"status": "healthy"}`
- **Monitoring**: Set up automated health check monitoring

### Logging

- **Location**: `backend/logs/`
- **Rotation**: Configure log rotation
- **Monitoring**: Set up log aggregation and alerting

### Performance Monitoring

Monitor these metrics:
- Response times
- Error rates
- Database connection pool usage
- Memory and CPU usage
- Disk space

### Backup Strategy

1. **Database**: Daily automated backups
2. **Files**: Regular backup of uploaded files
3. **Configuration**: Version control for configuration files
4. **Testing**: Regular backup restoration testing

## 🔄 Updates & Maintenance

### Application Updates

1. **Backup**: Create full backup before updates
2. **Test**: Test updates in staging environment
3. **Deploy**: Use blue-green or rolling deployment
4. **Verify**: Run health checks after deployment

### Database Migrations

```bash
# Production migration
cd backend
npm run db:migrate deploy
```

### Security Updates

- Regular dependency updates
- Security patch management
- SSL certificate renewal
- Access review and rotation

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check DATABASE_URL configuration
   - Verify database server accessibility
   - Check connection pool limits

2. **Authentication Issues**
   - Verify JWT secrets are properly set
   - Check token expiration settings
   - Validate CORS configuration

3. **Performance Issues**
   - Monitor database query performance
   - Check memory usage
   - Review rate limiting settings

### Emergency Procedures

1. **Service Restart**
```bash
# If using systemd
sudo systemctl restart agentic-talent-pro

# Manual restart
pkill -f "node.*index.js"
cd backend && npm start
```

2. **Database Recovery**
```bash
# Restore from backup
psql -h host -U user -d database < backup.sql
```

3. **Rollback Procedure**
```bash
# Revert to previous version
git checkout previous-tag
npm run build
npm start
```

## 📞 Support

For production support:
- Check logs in `backend/logs/`
- Review health check endpoint
- Monitor system resources
- Contact system administrator

## 🔐 Security Incident Response

1. **Immediate**: Isolate affected systems
2. **Assess**: Determine scope of incident
3. **Contain**: Stop ongoing threats
4. **Recover**: Restore from clean backups
5. **Review**: Analyze and improve security
