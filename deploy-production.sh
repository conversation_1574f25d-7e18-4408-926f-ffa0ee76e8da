#!/bin/bash

# Production Deployment Script for Agentic Talent Pro
# This script ensures production-ready deployment with security checks

set -e  # Exit on any error

echo "🚀 Starting production deployment for Agentic Talent Pro..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_error "Do not run this script as root for security reasons"
    exit 1
fi

# Check Node.js version
print_status "Checking Node.js version..."
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION'))" 2>/dev/null; then
    print_error "Node.js version $REQUIRED_VERSION or higher is required. Current: $NODE_VERSION"
    exit 1
fi
print_success "Node.js version check passed: $NODE_VERSION"

# Check if .env file exists
if [ ! -f "backend/.env" ]; then
    print_error "Production .env file not found!"
    print_status "Please copy backend/.env.production.example to backend/.env and configure it"
    exit 1
fi

# Security checks for .env file
print_status "Performing security checks on environment configuration..."

# Check for default JWT secrets
if grep -q "your-super-secret-jwt-key-here\|your-secret-key\|REPLACE_WITH_SECURE" backend/.env; then
    print_error "Default JWT secrets detected in .env file!"
    print_status "Please generate secure JWT secrets using: openssl rand -hex 64"
    exit 1
fi

# Check for localhost in CORS_ORIGIN in production
if grep -q "NODE_ENV=production" backend/.env && grep -q "localhost" backend/.env; then
    print_warning "Localhost detected in production environment configuration"
    print_status "Please update CORS_ORIGIN to your production domain"
fi

print_success "Environment security checks passed"

# Install dependencies
print_status "Installing dependencies..."
npm ci --only=production
cd backend && npm ci --only=production && cd ..
cd frontend && npm ci --only=production && cd ..
cd shared && npm ci --only=production && cd ..
print_success "Dependencies installed"

# Build shared package
print_status "Building shared package..."
cd shared && npm run build && cd ..
print_success "Shared package built"

# Build backend
print_status "Building backend..."
cd backend && npm run build && cd ..
print_success "Backend built"

# Build frontend
print_status "Building frontend..."
cd frontend && npm run build && cd ..
print_success "Frontend built"

# Database checks
print_status "Checking database connection..."
cd backend
if ! npm run db:generate > /dev/null 2>&1; then
    print_error "Failed to generate Prisma client"
    exit 1
fi

# Check if database is accessible
if ! node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.\$connect()
  .then(() => { console.log('Database connection successful'); process.exit(0); })
  .catch((e) => { console.error('Database connection failed:', e.message); process.exit(1); });
" 2>/dev/null; then
    print_error "Database connection failed"
    print_status "Please check your DATABASE_URL in backend/.env"
    exit 1
fi
cd ..

print_success "Database connection verified"

# Run database migrations
print_status "Running database migrations..."
cd backend && npm run db:migrate deploy && cd ..
print_success "Database migrations completed"

# Create logs directory
print_status "Creating logs directory..."
mkdir -p backend/logs
mkdir -p logs
print_success "Logs directory created"

# Set proper file permissions
print_status "Setting file permissions..."
chmod -R 755 .
chmod 600 backend/.env
print_success "File permissions set"

# Production readiness check
print_status "Running production readiness check..."
cd backend
if ! node -e "
const { validateProductionEnvironment } = require('./dist/config/production');
try {
  validateProductionEnvironment();
  console.log('Production environment validation passed');
} catch (error) {
  console.error('Production validation failed:', error.message);
  process.exit(1);
}
" 2>/dev/null; then
    print_error "Production readiness check failed"
    exit 1
fi
cd ..

print_success "Production readiness check passed"

# Create systemd service file (optional)
if command -v systemctl >/dev/null 2>&1; then
    print_status "Creating systemd service file..."
    cat > agentic-talent-pro.service << EOF
[Unit]
Description=Agentic Talent Pro API Server
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)/backend
ExecStart=/usr/bin/node dist/index.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF
    print_status "Systemd service file created: agentic-talent-pro.service"
    print_status "To install: sudo mv agentic-talent-pro.service /etc/systemd/system/"
    print_status "To enable: sudo systemctl enable agentic-talent-pro"
    print_status "To start: sudo systemctl start agentic-talent-pro"
fi

# Final deployment summary
echo ""
print_success "🎉 Production deployment completed successfully!"
echo ""
print_status "Deployment Summary:"
echo "  ✅ Dependencies installed"
echo "  ✅ Applications built"
echo "  ✅ Database migrations applied"
echo "  ✅ Security checks passed"
echo "  ✅ Production environment validated"
echo ""
print_status "To start the application:"
echo "  Backend: cd backend && npm start"
echo "  Frontend: cd frontend && npm start"
echo ""
print_status "Production URLs:"
echo "  API: http://localhost:3003"
echo "  Frontend: http://localhost:3002"
echo "  Health Check: http://localhost:3003/health"
echo "  API Docs: http://localhost:3003/api/docs"
echo ""
print_warning "Important Production Notes:"
echo "  🔒 Ensure HTTPS is configured via reverse proxy"
echo "  🔒 Set up database backups"
echo "  🔒 Configure monitoring and alerting"
echo "  🔒 Set up log rotation"
echo "  🔒 Configure firewall rules"
echo "  🔒 Regular security updates"
echo ""
print_success "Deployment completed! 🚀"
