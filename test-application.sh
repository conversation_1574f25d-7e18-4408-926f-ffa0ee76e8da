#!/bin/bash

# Test script to verify Agentic Talent Pro is working correctly
echo "🧪 Testing Agentic Talent Pro Application..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Test backend health
print_test "Testing backend health endpoint..."
if curl -s http://localhost:3003/health > /dev/null; then
    print_success "Backend is running on port 3003"
else
    print_error "Backend is not responding on port 3003"
    exit 1
fi

# Test frontend
print_test "Testing frontend..."
if curl -s http://localhost:3002 > /dev/null; then
    print_success "Frontend is running on port 3002"
else
    print_error "Frontend is not responding on port 3002"
    exit 1
fi

# Test API endpoints
print_test "Testing API endpoints..."

# Test login endpoint
print_test "Testing login API..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:3003/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$LOGIN_RESPONSE" | grep -q "token"; then
    print_success "Login API is working"
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
else
    print_error "Login API failed"
    echo "Response: $LOGIN_RESPONSE"
    exit 1
fi

# Test authenticated endpoint
print_test "Testing authenticated API..."
PROFILE_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:3003/api/auth/profile)

if echo "$PROFILE_RESPONSE" | grep -q "<EMAIL>"; then
    print_success "Authentication is working"
else
    print_error "Authentication failed"
    echo "Response: $PROFILE_RESPONSE"
    exit 1
fi

# Test projects API
print_test "Testing projects API..."
PROJECTS_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:3003/api/projects)

if echo "$PROJECTS_RESPONSE" | grep -q "data"; then
    print_success "Projects API is working"
else
    print_error "Projects API failed"
    echo "Response: $PROJECTS_RESPONSE"
fi

# Test dashboard API
print_test "Testing dashboard API..."
DASHBOARD_RESPONSE=$(curl -s -H "Authorization: Bearer $TOKEN" http://localhost:3003/api/dashboard/stats)

if echo "$DASHBOARD_RESPONSE" | grep -q "overview"; then
    print_success "Dashboard API is working"
else
    print_error "Dashboard API failed"
    echo "Response: $DASHBOARD_RESPONSE"
fi

echo ""
print_success "🎉 All tests passed! Your application is working correctly."
echo ""
echo -e "${BLUE}Application URLs:${NC}"
echo "  📊 Frontend: http://localhost:3002"
echo "  🔗 Backend API: http://localhost:3003"
echo "  📚 API Documentation: http://localhost:3003/api/docs"
echo "  🏥 Health Check: http://localhost:3003/health"
echo ""
echo -e "${BLUE}Demo Login Credentials:${NC}"
echo "  👤 Admin: <EMAIL> / admin123"
echo "  👤 Project Manager: <EMAIL> / pm123"
echo "  👤 HR Manager: <EMAIL> / hr123"
echo "  👤 Billing Manager: <EMAIL> / billing123"
echo "  👤 Resource: <EMAIL> / resource123"
echo "  👤 Client: <EMAIL> / client123"
echo ""
echo -e "${GREEN}✅ Your Agentic Talent Pro application is fully functional!${NC}"
