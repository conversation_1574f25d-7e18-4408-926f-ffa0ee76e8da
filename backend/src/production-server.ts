import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { Pool } from 'pg';
import { body, validationResult, query } from 'express-validator';
import { v4 as uuidv4 } from 'uuid';
import winston from 'winston';

const app = express();
const PORT = process.env.PORT || 3003;

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'agentic-talent-pro-production' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
  ],
});

// Add console transport for non-production
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

// Validate required environment variables
const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET', 'JWT_REFRESH_SECRET'];
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    logger.error(`❌ Missing required environment variable: ${envVar}`);
    process.exit(1);
  }
}

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(limiter);
app.use(cors({
  origin: [
    'http://localhost:3002',
    'http://localhost:3000',
    process.env.CORS_ORIGIN || 'http://localhost:3002'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'],
  preflightContinue: false,
  optionsSuccessStatus: 204
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Authentication middleware
const authenticate = async (req: any, res: any, next: any) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;

    const userResult = await pool.query('SELECT * FROM users WHERE id = $1', [decoded.id]);
    const user = userResult.rows[0];

    if (!user || user.status !== 'ACTIVE') {
      return res.status(401).json({ success: false, error: 'Invalid token' });
    }

    req.user = { id: user.id, email: user.email, role: user.role };
    next();
  } catch (error) {
    res.status(401).json({ success: false, error: 'Invalid token' });
  }
};

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
  });
});

// Auth endpoints
app.post('/api/auth/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 1 }),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const { email, password } = req.body;

    const userResult = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
    const user = userResult.rows[0];

    if (!user) {
      return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    if (user.status !== 'ACTIVE') {
      return res.status(401).json({ success: false, error: 'Account is not active' });
    }

    const tokenPayload = { id: user.id, email: user.email, role: user.role };
    const token = jwt.sign(tokenPayload, process.env.JWT_SECRET!, { expiresIn: '7d' });
    const refreshToken = jwt.sign(tokenPayload, process.env.JWT_REFRESH_SECRET!, { expiresIn: '30d' });

    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      data: { user: userWithoutPassword, token, refreshToken },
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.get('/api/auth/profile', authenticate, async (req: any, res: any) => {
  try {
    const userResult = await pool.query(`
      SELECT u.*, r.id as resource_id, r."employeeId", r.designation, r.department 
      FROM users u 
      LEFT JOIN resources r ON u.id = r."userId" 
      WHERE u.id = $1
    `, [req.user.id]);
    
    const user = userResult.rows[0];
    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    const { password: _, ...userWithoutPassword } = user;
    res.json({ success: true, data: userWithoutPassword });
  } catch (error) {
    console.error('Profile error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.post('/api/auth/logout', authenticate, (req: any, res: any) => {
  res.json({ success: true, message: 'Logout successful' });
});

// Dashboard endpoints
app.get('/api/dashboard/stats', authenticate, async (req: any, res: any) => {
  try {
    const [contractsResult, projectsResult, resourcesResult, timesheetsResult, invoicesResult] = await Promise.all([
      pool.query('SELECT COUNT(*) FROM contracts'),
      pool.query('SELECT COUNT(*) FROM projects WHERE status = $1', ['ACTIVE']),
      pool.query('SELECT COUNT(*) FROM resources WHERE status = $1', ['AVAILABLE']),
      pool.query('SELECT COUNT(*) FROM timesheets WHERE status = $1', ['SUBMITTED']),
      pool.query('SELECT COUNT(*) FROM invoices WHERE status IN ($1, $2)', ['SENT', 'OVERDUE']),
    ]);

    const stats: any = {
      overview: {
        totalContracts: parseInt(contractsResult.rows[0].count),
        activeProjects: parseInt(projectsResult.rows[0].count),
        totalResources: parseInt(resourcesResult.rows[0].count),
        pendingTimesheets: parseInt(timesheetsResult.rows[0].count),
        unpaidInvoices: parseInt(invoicesResult.rows[0].count),
      },
    };

    // Role-specific stats
    if (req.user.role === 'PROJECT_MANAGER') {
      const myProjectsResult = await pool.query('SELECT COUNT(*) FROM projects WHERE "managerId" = $1', [req.user.id]);
      stats.myProjects = parseInt(myProjectsResult.rows[0].count);
    }

    if (req.user.role === 'RESOURCE') {
      const resourceResult = await pool.query('SELECT id FROM resources WHERE "userId" = $1', [req.user.id]);
      if (resourceResult.rows[0]) {
        const resourceId = resourceResult.rows[0].id;
        const [tasksResult, timesheetsResult] = await Promise.all([
          pool.query('SELECT COUNT(*) FROM tasks WHERE "assignedToId" = $1', [req.user.id]),
          pool.query('SELECT COUNT(*) FROM timesheets WHERE "resourceId" = $1', [resourceId]),
        ]);
        stats.myTasks = parseInt(tasksResult.rows[0].count);
        stats.myTimesheets = parseInt(timesheetsResult.rows[0].count);
      }
    }

    res.json({ success: true, data: stats });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.get('/api/dashboard/charts', authenticate, async (req: any, res: any) => {
  try {
    const [projectStatusResult, resourceUtilizationResult, monthlyInvoicesResult] = await Promise.all([
      pool.query('SELECT status, COUNT(*) as count FROM projects GROUP BY status'),
      pool.query('SELECT status, COUNT(*) as count FROM resources GROUP BY status'),
      pool.query(`
        SELECT 
          TO_CHAR("createdAt", 'YYYY-MM') as month,
          SUM(total) as total,
          COUNT(*) as count
        FROM invoices 
        WHERE "createdAt" >= NOW() - INTERVAL '6 months'
        GROUP BY TO_CHAR("createdAt", 'YYYY-MM')
        ORDER BY month
      `),
    ]);

    const charts = {
      projectStatus: projectStatusResult.rows,
      resourceUtilization: resourceUtilizationResult.rows,
      monthlyInvoices: monthlyInvoicesResult.rows,
    };

    res.json({ success: true, data: charts });
  } catch (error) {
    console.error('Dashboard charts error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Contracts endpoints
app.get('/api/contracts', authenticate, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['DRAFT', 'ACTIVE', 'COMPLETED', 'TERMINATED']),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status;

    let whereClause = '';
    const params: any[] = [];
    
    if (status) {
      whereClause = 'WHERE c.status = $1';
      params.push(status);
    }

    // If user is a client, only show their contracts
    if (req.user.role === 'CLIENT') {
      whereClause = whereClause ? `${whereClause} AND c."clientId" = $${params.length + 1}` : `WHERE c."clientId" = $1`;
      params.push(req.user.id);
    }

    const query = `
      SELECT c.*, u."firstName", u."lastName", u.email,
             COUNT(p.id) as project_count
      FROM contracts c 
      JOIN users u ON c."clientId" = u.id 
      LEFT JOIN projects p ON c.id = p."contractId"
      ${whereClause}
      GROUP BY c.id, u."firstName", u."lastName", u.email
      ORDER BY c."createdAt" DESC 
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;

    const countQuery = `
      SELECT COUNT(*) FROM contracts c ${whereClause}
    `;

    params.push(limit, skip);
    const [result, countResult] = await Promise.all([
      pool.query(query, params),
      pool.query(countQuery, params.slice(0, -2)),
    ]);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].count),
        totalPages: Math.ceil(parseInt(countResult.rows[0].count) / limit),
      },
    });
  } catch (error) {
    console.error('Contracts error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Projects endpoints
app.get('/api/projects', authenticate, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['PLANNING', 'ACTIVE', 'ON_HOLD', 'COMPLETED', 'CANCELLED']),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status;

    let whereClause = '';
    const params: any[] = [];

    if (status) {
      whereClause = 'WHERE p.status = $1';
      params.push(status);
    }

    // If user is a project manager, only show their projects
    if (req.user.role === 'PROJECT_MANAGER') {
      whereClause = whereClause ? `${whereClause} AND p."managerId" = $${params.length + 1}` : `WHERE p."managerId" = $1`;
      params.push(req.user.id);
    }

    const query = `
      SELECT p.*, c.title as contract_title, u."firstName", u."lastName"
      FROM projects p
      JOIN contracts c ON p."contractId" = c.id
      JOIN users u ON p."managerId" = u.id
      ${whereClause}
      ORDER BY p."createdAt" DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;

    const countQuery = `
      SELECT COUNT(*) FROM projects p ${whereClause}
    `;

    params.push(limit, skip);
    const [result, countResult] = await Promise.all([
      pool.query(query, params),
      pool.query(countQuery, params.slice(0, -2)),
    ]);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].count),
        totalPages: Math.ceil(parseInt(countResult.rows[0].count) / limit),
      },
    });
  } catch (error) {
    console.error('Projects error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Resources endpoints
app.get('/api/resources', authenticate, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['AVAILABLE', 'ALLOCATED', 'ON_LEAVE', 'TERMINATED']),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 12;
    const skip = (page - 1) * limit;
    const status = req.query.status;

    let whereClause = '';
    const params: any[] = [];

    if (status) {
      whereClause = 'WHERE r.status = $1';
      params.push(status);
    }

    const query = `
      SELECT r.*, u."firstName", u."lastName", u.email, u.phone,
             json_agg(
               json_build_object(
                 'skill', json_build_object('name', s.name),
                 'proficiencyLevel', rs."proficiencyLevel",
                 'yearsOfExperience', rs."yearsOfExperience",
                 'certified', rs.certified
               )
             ) FILTER (WHERE s.id IS NOT NULL) as skills
      FROM resources r
      JOIN users u ON r."userId" = u.id
      LEFT JOIN resource_skills rs ON r.id = rs."resourceId"
      LEFT JOIN skills s ON rs."skillId" = s.id
      ${whereClause}
      GROUP BY r.id, u."firstName", u."lastName", u.email, u.phone
      ORDER BY r."createdAt" DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;

    const countQuery = `
      SELECT COUNT(*) FROM resources r ${whereClause}
    `;

    params.push(limit, skip);
    const [result, countResult] = await Promise.all([
      pool.query(query, params),
      pool.query(countQuery, params.slice(0, -2)),
    ]);

    // Process the results to include user data
    const processedResults = result.rows.map(row => ({
      ...row,
      user: {
        firstName: row.firstName,
        lastName: row.lastName,
        email: row.email,
        phone: row.phone,
      },
      skills: row.skills || [],
    }));

    res.json({
      success: true,
      data: processedResults,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].count),
        totalPages: Math.ceil(parseInt(countResult.rows[0].count) / limit),
      },
    });
  } catch (error) {
    console.error('Resources error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Tasks endpoints
app.get('/api/tasks', authenticate, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['TODO', 'IN_PROGRESS', 'REVIEW', 'COMPLETED', 'CANCELLED']),
  query('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status;
    const priority = req.query.priority;

    let whereClause = '';
    const params: any[] = [];

    if (status) {
      whereClause = 'WHERE t.status = $1';
      params.push(status);
    }

    if (priority) {
      whereClause = whereClause ? `${whereClause} AND t.priority = $${params.length + 1}` : `WHERE t.priority = $1`;
      params.push(priority);
    }

    // If user is a resource, only show their tasks
    if (req.user.role === 'RESOURCE') {
      whereClause = whereClause ? `${whereClause} AND t."assignedToId" = $${params.length + 1}` : `WHERE t."assignedToId" = $1`;
      params.push(req.user.id);
    }

    const query = `
      SELECT t.*, p.name as project_name, u."firstName", u."lastName"
      FROM tasks t
      JOIN projects p ON t."projectId" = p.id
      LEFT JOIN users u ON t."assignedToId" = u.id
      ${whereClause}
      ORDER BY t."createdAt" DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;

    const countQuery = `
      SELECT COUNT(*) FROM tasks t ${whereClause}
    `;

    params.push(limit, skip);
    const [result, countResult] = await Promise.all([
      pool.query(query, params),
      pool.query(countQuery, params.slice(0, -2)),
    ]);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].count),
        totalPages: Math.ceil(parseInt(countResult.rows[0].count) / limit),
      },
    });
  } catch (error) {
    logger.error('Tasks error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.post('/api/tasks', authenticate, [
  body('title').isLength({ min: 1 }).trim(),
  body('description').optional().trim(),
  body('projectId').isLength({ min: 1 }),
  body('assignedToId').optional().isLength({ min: 1 }),
  body('priority').isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  body('estimatedHours').isFloat({ min: 0 }),
  body('dueDate').isISO8601(),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const { title, description, projectId, assignedToId, priority, estimatedHours, dueDate } = req.body;
    const taskId = uuidv4();

    const result = await pool.query(`
      INSERT INTO tasks (id, title, description, "projectId", "assignedToId", priority, "estimatedHours", "dueDate", status, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 'TODO', NOW(), NOW())
      RETURNING *
    `, [taskId, title, description, projectId, assignedToId, priority, estimatedHours, dueDate]);

    res.status(201).json({ success: true, data: result.rows[0] });
  } catch (error) {
    logger.error('Create task error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.get('/api/tasks/:id', authenticate, async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      SELECT t.*, p.name as project_name, u."firstName", u."lastName"
      FROM tasks t
      JOIN projects p ON t."projectId" = p.id
      LEFT JOIN users u ON t."assignedToId" = u.id
      WHERE t.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Task not found' });
    }

    res.json({ success: true, data: result.rows[0] });
  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.put('/api/tasks/:id', authenticate, [
  body('title').optional().isLength({ min: 1 }).trim(),
  body('description').optional().trim(),
  body('status').optional().isIn(['TODO', 'IN_PROGRESS', 'REVIEW', 'COMPLETED', 'CANCELLED']),
  body('priority').optional().isIn(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  body('estimatedHours').optional().isFloat({ min: 0 }),
  body('actualHours').optional().isFloat({ min: 0 }),
  body('dueDate').optional().isISO8601(),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const { id } = req.params;
    const updates = req.body;

    const setClause = Object.keys(updates).map((key, index) => `"${key}" = $${index + 2}`).join(', ');
    const values = [id, ...Object.values(updates)];

    const result = await pool.query(`
      UPDATE tasks SET ${setClause}, "updatedAt" = NOW()
      WHERE id = $1
      RETURNING *
    `, values);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Task not found' });
    }

    res.json({ success: true, data: result.rows[0] });
  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.delete('/api/tasks/:id', authenticate, async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const result = await pool.query('DELETE FROM tasks WHERE id = $1 RETURNING *', [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Task not found' });
    }

    res.json({ success: true, message: 'Task deleted successfully' });
  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Timesheets endpoints
app.get('/api/timesheets', authenticate, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['DRAFT', 'SUBMITTED', 'APPROVED', 'REJECTED']),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status;

    let whereClause = '';
    const params: any[] = [];

    if (status) {
      whereClause = 'WHERE ts.status = $1';
      params.push(status);
    }

    // If user is a resource, only show their timesheets
    if (req.user.role === 'RESOURCE') {
      const resourceResult = await pool.query('SELECT id FROM resources WHERE "userId" = $1', [req.user.id]);
      if (resourceResult.rows[0]) {
        const resourceId = resourceResult.rows[0].id;
        whereClause = whereClause ? `${whereClause} AND ts."resourceId" = $${params.length + 1}` : `WHERE ts."resourceId" = $1`;
        params.push(resourceId);
      }
    }

    const query = `
      SELECT ts.*, r."employeeId", u."firstName", u."lastName", p.name as project_name
      FROM timesheets ts
      JOIN resources r ON ts."resourceId" = r.id
      JOIN users u ON r."userId" = u.id
      LEFT JOIN projects p ON ts."projectId" = p.id
      ${whereClause}
      ORDER BY ts."weekEnding" DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;

    const countQuery = `
      SELECT COUNT(*) FROM timesheets ts ${whereClause}
    `;

    params.push(limit, skip);
    const [result, countResult] = await Promise.all([
      pool.query(query, params),
      pool.query(countQuery, params.slice(0, -2)),
    ]);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].count),
        totalPages: Math.ceil(parseInt(countResult.rows[0].count) / limit),
      },
    });
  } catch (error) {
    console.error('Timesheets error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.post('/api/timesheets', authenticate, [
  body('resourceId').isLength({ min: 1 }),
  body('projectId').optional().isLength({ min: 1 }),
  body('weekEnding').isISO8601(),
  body('regularHours').isFloat({ min: 0, max: 168 }),
  body('overtimeHours').optional().isFloat({ min: 0 }),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const { resourceId, projectId, weekEnding, regularHours, overtimeHours = 0 } = req.body;
    const timesheetId = uuidv4();
    const totalHours = regularHours + overtimeHours;

    const result = await pool.query(`
      INSERT INTO timesheets (id, "resourceId", "projectId", "weekEnding", "regularHours", "overtimeHours", "totalHours", status, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7, 'DRAFT', NOW(), NOW())
      RETURNING *
    `, [timesheetId, resourceId, projectId, weekEnding, regularHours, overtimeHours, totalHours]);

    res.status(201).json({ success: true, data: result.rows[0] });
  } catch (error) {
    logger.error('Create timesheet error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.put('/api/timesheets/:id', authenticate, [
  body('regularHours').optional().isFloat({ min: 0, max: 168 }),
  body('overtimeHours').optional().isFloat({ min: 0 }),
  body('status').optional().isIn(['DRAFT', 'SUBMITTED', 'APPROVED', 'REJECTED']),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const { id } = req.params;
    const updates = req.body;

    // Recalculate total hours if regular or overtime hours are updated
    if (updates.regularHours !== undefined || updates.overtimeHours !== undefined) {
      const currentResult = await pool.query('SELECT "regularHours", "overtimeHours" FROM timesheets WHERE id = $1', [id]);
      if (currentResult.rows[0]) {
        const current = currentResult.rows[0];
        const newRegular = updates.regularHours !== undefined ? updates.regularHours : current.regularHours;
        const newOvertime = updates.overtimeHours !== undefined ? updates.overtimeHours : current.overtimeHours;
        updates.totalHours = newRegular + newOvertime;
      }
    }

    const setClause = Object.keys(updates).map((key, index) => `"${key}" = $${index + 2}`).join(', ');
    const values = [id, ...Object.values(updates)];

    const result = await pool.query(`
      UPDATE timesheets SET ${setClause}, "updatedAt" = NOW()
      WHERE id = $1
      RETURNING *
    `, values);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Timesheet not found' });
    }

    res.json({ success: true, data: result.rows[0] });
  } catch (error) {
    console.error('Update timesheet error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.post('/api/timesheets/:id/submit', authenticate, async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      UPDATE timesheets SET status = 'SUBMITTED', "submittedAt" = NOW(), "updatedAt" = NOW()
      WHERE id = $1 AND status = 'DRAFT'
      RETURNING *
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Timesheet not found or not in draft status' });
    }

    res.json({ success: true, data: result.rows[0] });
  } catch (error) {
    console.error('Submit timesheet error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.post('/api/timesheets/:id/approve', authenticate, async (req: any, res: any) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      UPDATE timesheets SET status = 'APPROVED', "approvedAt" = NOW(), "approvedBy" = $2, "updatedAt" = NOW()
      WHERE id = $1 AND status = 'SUBMITTED'
      RETURNING *
    `, [id, req.user.id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Timesheet not found or not in submitted status' });
    }

    res.json({ success: true, data: result.rows[0] });
  } catch (error) {
    console.error('Approve timesheet error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.post('/api/timesheets/:id/reject', authenticate, [
  body('reason').isLength({ min: 1 }).trim(),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const { id } = req.params;
    const { reason } = req.body;

    const result = await pool.query(`
      UPDATE timesheets SET status = 'REJECTED', "rejectedAt" = NOW(), "rejectedBy" = $2, "rejectionReason" = $3, "updatedAt" = NOW()
      WHERE id = $1 AND status = 'SUBMITTED'
      RETURNING *
    `, [id, req.user.id, reason]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Timesheet not found or not in submitted status' });
    }

    res.json({ success: true, data: result.rows[0] });
  } catch (error) {
    console.error('Reject timesheet error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Invoices endpoints
app.get('/api/invoices', authenticate, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED']),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status;

    let whereClause = '';
    const params: any[] = [];

    if (status) {
      whereClause = 'WHERE i.status = $1';
      params.push(status);
    }

    const query = `
      SELECT i.*, c.title as contract_title, u."firstName", u."lastName"
      FROM invoices i
      LEFT JOIN contracts c ON i."contractId" = c.id
      LEFT JOIN users u ON c."clientId" = u.id
      ${whereClause}
      ORDER BY i."createdAt" DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;

    const countQuery = `
      SELECT COUNT(*) FROM invoices i ${whereClause}
    `;

    params.push(limit, skip);
    const [result, countResult] = await Promise.all([
      pool.query(query, params),
      pool.query(countQuery, params.slice(0, -2)),
    ]);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].count),
        totalPages: Math.ceil(parseInt(countResult.rows[0].count) / limit),
      },
    });
  } catch (error) {
    console.error('Invoices error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Vendors endpoints
app.get('/api/vendors', authenticate, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['ACTIVE', 'INACTIVE', 'BLACKLISTED']),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status;

    let whereClause = '';
    const params: any[] = [];

    if (status) {
      whereClause = 'WHERE v.status = $1';
      params.push(status);
    }

    const query = `
      SELECT v.*,
             COUNT(r.id) as resource_count
      FROM vendors v
      LEFT JOIN resources r ON v.id = r."vendorId"
      ${whereClause}
      GROUP BY v.id
      ORDER BY v."createdAt" DESC
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;

    const countQuery = `
      SELECT COUNT(*) FROM vendors v ${whereClause}
    `;

    params.push(limit, skip);
    const [result, countResult] = await Promise.all([
      pool.query(query, params),
      pool.query(countQuery, params.slice(0, -2)),
    ]);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].count),
        totalPages: Math.ceil(parseInt(countResult.rows[0].count) / limit),
      },
    });
  } catch (error) {
    logger.error('Vendors error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Start server
app.listen(PORT, () => {
  logger.info(`🚀 Production server running on port ${PORT}`);
  logger.info(`📊 Dashboard: http://localhost:3002`);
  logger.info(`🔗 API: http://localhost:${PORT}`);
  logger.info(`📚 Features: Authentication, RBAC, Dashboard, Contracts, Projects, Resources, Tasks, Timesheets, Invoices, Vendors`);
});

export default app;
