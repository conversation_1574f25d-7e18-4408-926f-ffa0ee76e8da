import { validateProductionEnvironment } from './config/production';
import { logger } from './utils/logger';
import { prisma } from './config/database';
import fs from 'fs';
import path from 'path';

/**
 * Production startup validation and initialization
 */
export async function initializeProduction() {
  try {
    logger.info('🚀 Starting production initialization...');

    // 1. Validate environment variables
    logger.info('📋 Validating environment configuration...');
    const config = validateProductionEnvironment();
    logger.info('✅ Environment configuration validated');

    // 2. Ensure logs directory exists
    const logDir = path.dirname(config.logging.file);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
      logger.info(`📁 Created logs directory: ${logDir}`);
    }

    // 3. Test database connection
    logger.info('🗄️  Testing database connection...');
    await prisma.$connect();
    await prisma.$queryRaw`SELECT 1`;
    logger.info('✅ Database connection successful');

    // 4. Validate database schema
    logger.info('🔍 Validating database schema...');
    try {
      // Check if required tables exist
      const tables = await prisma.$queryRaw`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
      ` as any[];

      const requiredTables = [
        'users', 'contracts', 'projects', 'resources', 
        'tasks', 'timesheets', 'invoices', 'vendors'
      ];

      const existingTables = tables.map((t: any) => t.table_name);
      const missingTables = requiredTables.filter(table => !existingTables.includes(table));

      if (missingTables.length > 0) {
        throw new Error(`Missing database tables: ${missingTables.join(', ')}`);
      }

      logger.info('✅ Database schema validation successful');
    } catch (error) {
      logger.error('❌ Database schema validation failed:', error);
      throw error;
    }

    // 5. Check for production readiness
    logger.info('🔒 Checking production readiness...');
    
    // Warn about development seeds in production
    if (config.server.nodeEnv === 'production') {
      try {
        const adminUser = await prisma.user.findUnique({
          where: { email: '<EMAIL>' }
        });
        
        if (adminUser) {
          logger.warn('⚠️  Development admin user found in production database!');
          logger.warn('🔒 Please create secure admin credentials and remove development users.');
        }
      } catch (error) {
        // Ignore if user table doesn't exist yet
      }
    }

    // 6. Security checks
    if (config.server.corsOrigin.includes('localhost') && config.server.nodeEnv === 'production') {
      logger.warn('⚠️  CORS origin includes localhost in production');
    }

    if (config.jwt.secret.length < 32) {
      logger.warn('⚠️  JWT secret is shorter than recommended 32 characters');
    }

    logger.info('✅ Production initialization completed successfully');
    
    return {
      config,
      status: 'ready',
      timestamp: new Date().toISOString(),
    };

  } catch (error) {
    logger.error('❌ Production initialization failed:', error);
    throw error;
  }
}

/**
 * Graceful shutdown handler
 */
export async function gracefulShutdown(signal: string) {
  logger.info(`📴 Received ${signal}. Starting graceful shutdown...`);
  
  try {
    // Close database connections
    await prisma.$disconnect();
    logger.info('✅ Database connections closed');
    
    // Give time for ongoing requests to complete
    setTimeout(() => {
      logger.info('👋 Graceful shutdown completed');
      process.exit(0);
    }, 5000);
    
  } catch (error) {
    logger.error('❌ Error during graceful shutdown:', error);
    process.exit(1);
  }
}

/**
 * Health check function for production monitoring
 */
export async function healthCheck() {
  try {
    // Check database connectivity
    await prisma.$queryRaw`SELECT 1`;
    
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
    };
  } catch (error) {
    logger.error('Health check failed:', error);
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
