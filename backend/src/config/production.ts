import { logger } from '../utils/logger';

/**
 * Production Environment Configuration
 * Validates and configures production-specific settings
 */

export interface ProductionConfig {
  database: {
    url: string;
  };
  jwt: {
    secret: string;
    refreshSecret: string;
    expiresIn: string;
    refreshExpiresIn: string;
  };
  server: {
    port: number;
    corsOrigin: string;
    nodeEnv: string;
  };
  security: {
    rateLimitWindowMs: number;
    rateLimitMaxRequests: number;
    maxFileSize: number;
  };
  logging: {
    level: string;
    file: string;
  };
}

/**
 * Validates required environment variables for production
 */
export function validateProductionEnvironment(): ProductionConfig {
  const requiredEnvVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'CORS_ORIGIN'
  ];

  const missingVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missingVars.length > 0) {
    logger.error(`❌ Missing required environment variables: ${missingVars.join(', ')}`);
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  // Validate JWT secrets are not default values
  const defaultSecrets = [
    'your-super-secret-jwt-key-here',
    'your-super-secret-refresh-key-here',
    'your-secret-key',
    'your-refresh-secret'
  ];

  if (defaultSecrets.includes(process.env.JWT_SECRET!)) {
    logger.error('❌ JWT_SECRET is using default value. Please set a secure secret.');
    throw new Error('JWT_SECRET must be changed from default value');
  }

  if (defaultSecrets.includes(process.env.JWT_REFRESH_SECRET!)) {
    logger.error('❌ JWT_REFRESH_SECRET is using default value. Please set a secure secret.');
    throw new Error('JWT_REFRESH_SECRET must be changed from default value');
  }

  // Validate CORS origin is not localhost in production
  if (process.env.NODE_ENV === 'production' && process.env.CORS_ORIGIN?.includes('localhost')) {
    logger.warn('⚠️  CORS_ORIGIN contains localhost in production environment');
  }

  return {
    database: {
      url: process.env.DATABASE_URL!,
    },
    jwt: {
      secret: process.env.JWT_SECRET!,
      refreshSecret: process.env.JWT_REFRESH_SECRET!,
      expiresIn: process.env.JWT_EXPIRES_IN || '7d',
      refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
    },
    server: {
      port: parseInt(process.env.PORT || '3003'),
      corsOrigin: process.env.CORS_ORIGIN!,
      nodeEnv: process.env.NODE_ENV || 'production',
    },
    security: {
      rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
      rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
      maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'),
    },
    logging: {
      level: process.env.LOG_LEVEL || 'info',
      file: process.env.LOG_FILE || './logs/app.log',
    },
  };
}

/**
 * Security headers configuration for production
 */
export const securityHeaders = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for API compatibility
  crossOriginOpenerPolicy: false,
  crossOriginResourcePolicy: { policy: "cross-origin" },
  dnsPrefetchControl: true,
  frameguard: { action: 'deny' },
  hidePoweredBy: true,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  },
  ieNoOpen: true,
  noSniff: true,
  originAgentCluster: true,
  permittedCrossDomainPolicies: false,
  referrerPolicy: "no-referrer",
  xssFilter: true,
};

/**
 * Production-specific middleware configuration
 */
export const productionMiddleware = {
  compression: {
    level: 6,
    threshold: 1024,
  },
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: {
      success: false,
      error: 'Too many requests from this IP, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
  },
  morgan: {
    format: 'combined',
    skip: (req: any, res: any) => res.statusCode < 400, // Only log errors in production
  },
};
