import express from 'express';
import cors from 'cors';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { Pool } from 'pg';

const app = express();
const PORT = process.env.PORT || 3003;

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5433/agentic_talent_pro',
});

// Middleware
app.use(cors({
  origin: ['http://localhost:3002', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  preflightContinue: false,
  optionsSuccessStatus: 204
}));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Login endpoint
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const userResult = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
    const user = userResult.rows[0];

    if (!user) {
      return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    // Check if user is active
    if (user.status !== 'ACTIVE') {
      return res.status(401).json({ success: false, error: 'Account is not active' });
    }

    // Generate token
    const jwtSecret = process.env.JWT_SECRET || 'dev-jwt-secret-key-for-development-only';
    const refreshSecret = process.env.JWT_REFRESH_SECRET || 'dev-refresh-secret-key-for-development-only';

    const token = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      jwtSecret,
      { expiresIn: '7d' }
    );

    const refreshToken = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      refreshSecret,
      { expiresIn: '30d' }
    );

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      data: {
        user: userWithoutPassword,
        token,
        refreshToken,
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Authentication middleware
const authenticateToken = async (req: any, res: any, next: any) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const token = authHeader.substring(7);
    const jwtSecret = process.env.JWT_SECRET || 'dev-jwt-secret-key-for-development-only';
    const decoded = jwt.verify(token, jwtSecret) as any;

    const userResult = await pool.query('SELECT * FROM users WHERE id = $1', [decoded.id]);
    const user = userResult.rows[0];

    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    req.user = { id: user.id, email: user.email, role: user.role };
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(401).json({ success: false, error: 'Invalid token' });
  }
};

// Get profile endpoint
app.get('/api/auth/profile', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const token = authHeader.substring(7);
    const jwtSecret = process.env.JWT_SECRET || 'dev-jwt-secret-key-for-development-only';
    const decoded = jwt.verify(token, jwtSecret) as any;

    const userResult = await pool.query('SELECT * FROM users WHERE id = $1', [decoded.id]);
    const user = userResult.rows[0];

    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    const { password: _, ...userWithoutPassword } = user;
    res.json({ success: true, data: userWithoutPassword });
  } catch (error) {
    console.error('Profile error:', error);
    res.status(401).json({ success: false, error: 'Invalid token' });
  }
});

// Dashboard stats endpoint
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    const [contractsResult, projectsResult, resourcesResult, timesheetsResult, invoicesResult] = await Promise.all([
      pool.query('SELECT COUNT(*) FROM contracts'),
      pool.query('SELECT COUNT(*) FROM projects WHERE status = $1', ['ACTIVE']),
      pool.query('SELECT COUNT(*) FROM resources WHERE status = $1', ['AVAILABLE']),
      pool.query('SELECT COUNT(*) FROM timesheets WHERE status = $1', ['SUBMITTED']),
      pool.query('SELECT COUNT(*) FROM invoices WHERE status IN ($1, $2)', ['SENT', 'OVERDUE']),
    ]);

    const stats = {
      overview: {
        totalContracts: parseInt(contractsResult.rows[0].count),
        activeProjects: parseInt(projectsResult.rows[0].count),
        totalResources: parseInt(resourcesResult.rows[0].count),
        pendingTimesheets: parseInt(timesheetsResult.rows[0].count),
        unpaidInvoices: parseInt(invoicesResult.rows[0].count),
      },
    };

    res.json({ success: true, data: stats });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Dashboard charts endpoint
app.get('/api/dashboard/charts', async (req, res) => {
  try {
    const [projectStatusResult, resourceUtilizationResult] = await Promise.all([
      pool.query('SELECT status, COUNT(*) as count FROM projects GROUP BY status'),
      pool.query('SELECT status, COUNT(*) as count FROM resources GROUP BY status'),
    ]);

    const charts = {
      projectStatus: projectStatusResult.rows,
      resourceUtilization: resourceUtilizationResult.rows,
      monthlyInvoices: [], // Placeholder for now
    };

    res.json({ success: true, data: charts });
  } catch (error) {
    console.error('Dashboard charts error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Contracts endpoint
app.get('/api/contracts', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT c.*, u."firstName", u."lastName", u.email 
      FROM contracts c 
      JOIN users u ON c."clientId" = u.id 
      ORDER BY c."createdAt" DESC
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 10,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Contracts error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Projects endpoint
app.get('/api/projects', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT p.*, c.title as contract_title, u."firstName", u."lastName"
      FROM projects p 
      JOIN contracts c ON p."contractId" = c.id
      JOIN users u ON p."managerId" = u.id 
      ORDER BY p."createdAt" DESC
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 10,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Projects error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Resources endpoint
app.get('/api/resources', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT r.*, u."firstName", u."lastName", u.email, u.phone
      FROM resources r
      JOIN users u ON r."userId" = u.id
      ORDER BY r."createdAt" DESC
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Resources error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Tasks endpoint
app.get('/api/tasks', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    const result = await pool.query(`
      SELECT t.*, p.name as project_name, u."firstName", u."lastName"
      FROM tasks t
      JOIN projects p ON t."projectId" = p.id
      LEFT JOIN users u ON t."assignedToId" = u.id
      ORDER BY t."createdAt" DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset]);

    const countResult = await pool.query('SELECT COUNT(*) FROM tasks');
    const total = parseInt(countResult.rows[0].count);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Tasks error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Timesheets endpoint
app.get('/api/timesheets', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    const result = await pool.query(`
      SELECT ts.*, p.name as project_name, u."firstName", u."lastName"
      FROM timesheets ts
      JOIN resources r ON ts."resourceId" = r.id
      JOIN users u ON r."userId" = u.id
      JOIN projects p ON ts."projectId" = p.id
      ORDER BY ts."weekEnding" DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset]);

    const countResult = await pool.query('SELECT COUNT(*) FROM timesheets');
    const total = parseInt(countResult.rows[0].count);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Timesheets error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Invoices endpoint
app.get('/api/invoices', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    const result = await pool.query(`
      SELECT i.*, p.name as project_name, c.title as contract_title
      FROM invoices i
      JOIN projects p ON i."projectId" = p.id
      JOIN contracts c ON p."contractId" = c.id
      ORDER BY i."createdAt" DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset]);

    const countResult = await pool.query('SELECT COUNT(*) FROM invoices');
    const total = parseInt(countResult.rows[0].count);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Invoices error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Vendors endpoint
app.get('/api/vendors', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT * FROM vendors
      ORDER BY "createdAt" DESC
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Vendors error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create task endpoint
app.post('/api/tasks', authenticateToken, async (req, res) => {
  try {
    const { title, description, projectId, assignedToId, priority, estimatedHours, dueDate } = req.body;
    const taskId = `task-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO tasks (id, title, description, "projectId", "assignedToId", priority, "estimatedHours", "dueDate", status, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 'TODO', NOW(), NOW())
      RETURNING *
    `, [taskId, title, description, projectId, assignedToId, priority, estimatedHours, dueDate]);

    res.json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create timesheet endpoint
app.post('/api/timesheets', authenticateToken, async (req, res) => {
  try {
    const { resourceId, projectId, weekEnding, regularHours, overtimeHours = 0 } = req.body;
    const timesheetId = `timesheet-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO timesheets (id, "resourceId", "projectId", "weekEnding", "regularHours", "overtimeHours", status, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, 'DRAFT', NOW(), NOW())
      RETURNING *
    `, [timesheetId, resourceId, projectId, weekEnding, regularHours, overtimeHours]);

    res.json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error('Create timesheet error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Users endpoint for dropdowns
app.get('/api/users', authenticateToken, async (req, res) => {
  try {
    const { role } = req.query;
    let query = 'SELECT id, "firstName", "lastName", email, role FROM users WHERE status = \'ACTIVE\'';
    const params = [];

    if (role) {
      query += ' AND role = $1';
      params.push(role);
    }

    query += ' ORDER BY "firstName", "lastName"';

    const result = await pool.query(query, params);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Users error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Project by ID endpoint
app.get('/api/projects/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(`
      SELECT p.*,
             c.title as contract_title, c.value as contract_value,
             u."firstName" as manager_firstName, u."lastName" as manager_lastName, u.email as manager_email,
             cl."firstName" as client_firstName, cl."lastName" as client_lastName, cl.email as client_email
      FROM projects p
      JOIN contracts c ON p."contractId" = c.id
      JOIN users u ON p."managerId" = u.id
      JOIN users cl ON c."clientId" = cl.id
      WHERE p.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Project not found' });
    }

    const project = result.rows[0];

    // Get project resources
    const resourcesResult = await pool.query(`
      SELECT pr.*, r.*, u."firstName", u."lastName", u.email
      FROM project_resources pr
      JOIN resources r ON pr."resourceId" = r.id
      JOIN users u ON r."userId" = u.id
      WHERE pr."projectId" = $1
    `, [id]);

    // Get project tasks
    const tasksResult = await pool.query(`
      SELECT t.*, u."firstName", u."lastName"
      FROM tasks t
      LEFT JOIN users u ON t."assignedToId" = u.id
      WHERE t."projectId" = $1
      ORDER BY t."createdAt" DESC
    `, [id]);

    // Structure the response to match frontend expectations
    const projectData = {
      ...project,
      contract: {
        title: project.contract_title,
        value: project.contract_value,
        client: {
          firstName: project.client_firstName,
          lastName: project.client_lastName,
          email: project.client_email,
        },
      },
      manager: {
        firstName: project.manager_firstName,
        lastName: project.manager_lastName,
        email: project.manager_email,
      },
      projectResources: resourcesResult.rows.map(pr => ({
        id: pr.id,
        allocationPercent: pr.allocationPercent,
        resource: {
          user: {
            firstName: pr.firstName,
            lastName: pr.lastName,
            email: pr.email,
          },
        },
      })),
      tasks: tasksResult.rows,
    };

    res.json({
      success: true,
      data: projectData,
    });
  } catch (error) {
    console.error('Project by ID error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create project endpoint
app.post('/api/projects', authenticateToken, async (req, res) => {
  try {
    const { name, description, contractId, managerId, startDate, endDate, budget } = req.body;
    const projectId = `project-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO projects (id, name, description, "contractId", "managerId", "startDate", "endDate", budget, status, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 'PLANNING', NOW(), NOW())
      RETURNING *
    `, [projectId, name, description, contractId, managerId, startDate, endDate, budget]);

    res.json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error('Create project error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Update project endpoint
app.put('/api/projects/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, contractId, managerId, startDate, endDate, budget, status } = req.body;

    const result = await pool.query(`
      UPDATE projects
      SET name = $1, description = $2, "contractId" = $3, "managerId" = $4,
          "startDate" = $5, "endDate" = $6, budget = $7, status = $8, "updatedAt" = NOW()
      WHERE id = $9
      RETURNING *
    `, [name, description, contractId, managerId, startDate, endDate, budget, status, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Project not found' });
    }

    res.json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error('Update project error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Skills endpoint
app.get('/api/skills', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT * FROM skills
      ORDER BY category, name
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Skills error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Resource Plans endpoint
app.get('/api/resource-plans', authenticateToken, async (req, res) => {
  try {
    const { projectId } = req.query;
    let query = `
      SELECT rp.*, s.name as skill_name, s.category as skill_category,
             p.name as project_name
      FROM resource_plans rp
      JOIN skills s ON rp."skillId" = s.id
      JOIN projects p ON rp."projectId" = p.id
    `;
    const params = [];

    if (projectId) {
      query += ' WHERE rp."projectId" = $1';
      params.push(projectId);
    }

    query += ' ORDER BY rp."createdAt" DESC';

    const result = await pool.query(query, params);

    // Transform the data to match expected structure
    const resourcePlans = result.rows.map(row => ({
      ...row,
      skill: {
        id: row.skillId,
        name: row.skill_name,
        category: row.skill_category,
      },
      project: {
        id: row.projectId,
        name: row.project_name,
      },
      planAllocations: [], // Will be populated separately if needed
      resourceRequests: [], // Will be populated separately if needed
    }));

    res.json({
      success: true,
      data: resourcePlans,
      pagination: {
        page: 1,
        limit: 100,
        total: resourcePlans.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Resource plans error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create Resource Plan endpoint
app.post('/api/resource-plans', authenticateToken, async (req, res) => {
  try {
    const {
      projectId,
      skillId,
      role,
      allocationPercent,
      requiredCount,
      startDate,
      endDate,
      minExperience = 0,
      maxBudget,
      description,
    } = req.body;

    const resourcePlanId = `rp-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO resource_plans (
        id, "projectId", "skillId", role, "allocationPercent", "requiredCount",
        "startDate", "endDate", "minExperience", "maxBudget", description,
        status, "createdAt", "updatedAt"
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 'DRAFT', NOW(), NOW())
      RETURNING *
    `, [
      resourcePlanId, projectId, skillId, role, allocationPercent, requiredCount,
      startDate, endDate, minExperience, maxBudget, description
    ]);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Resource plan created successfully',
    });
  } catch (error) {
    console.error('Create resource plan error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Resource Matching endpoint
app.post('/api/resource-plans/:id/match-resources', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { limit = 10 } = req.body;

    // Get resource plan details
    const planResult = await pool.query(`
      SELECT rp.*, s.name as skill_name, s.category as skill_category
      FROM resource_plans rp
      JOIN skills s ON rp."skillId" = s.id
      WHERE rp.id = $1
    `, [id]);

    if (planResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource plan not found' });
    }

    const plan = planResult.rows[0];

    // Find resources with matching skills
    const resourcesResult = await pool.query(`
      SELECT DISTINCT r.*, u."firstName", u."lastName", u.email, u.phone,
             rs."proficiencyLevel", rs."yearsOfExperience", rs.certified
      FROM resources r
      JOIN users u ON r."userId" = u.id
      JOIN resource_skills rs ON r.id = rs."resourceId"
      WHERE r.status = 'AVAILABLE'
        AND rs."skillId" = $1
        AND rs."yearsOfExperience" >= $2
        AND ($3::numeric IS NULL OR r."hourlyRate" <= $3)
      ORDER BY rs."proficiencyLevel" DESC, rs."yearsOfExperience" DESC
      LIMIT $4
    `, [plan.skillId, plan.minExperience, plan.maxBudget, limit]);

    // Calculate match scores for each resource
    const matches = resourcesResult.rows.map(resource => {
      // Skill match (0-100) based on proficiency level (1-5)
      const skillMatch = (resource.proficiencyLevel / 5) * 100;

      // Experience match (0-100)
      const experienceMatch = resource.yearsOfExperience >= plan.minExperience
        ? Math.min(100, 80 + ((resource.yearsOfExperience - plan.minExperience) / plan.minExperience) * 20)
        : (resource.yearsOfExperience / plan.minExperience) * 60;

      // Budget match (0-100)
      const budgetMatch = plan.maxBudget
        ? (resource.hourlyRate <= plan.maxBudget ? 100 - ((resource.hourlyRate / plan.maxBudget) * 20) : 60)
        : 100;

      // Certification bonus
      const certificationBonus = resource.certified ? 10 : 0;

      // Overall match score (weighted average)
      const matchScore = Math.min(100,
        (skillMatch * 0.4) +
        (experienceMatch * 0.3) +
        (budgetMatch * 0.2) +
        (90 * 0.1) + // Availability (assuming 90% for now)
        certificationBonus
      );

      return {
        resourceId: resource.id,
        resource: {
          id: resource.id,
          user: {
            firstName: resource.firstName,
            lastName: resource.lastName,
            email: resource.email,
            phone: resource.phone,
          },
          designation: resource.designation,
          department: resource.department,
          hourlyRate: resource.hourlyRate,
          skills: [{
            skill: { name: plan.skill_name },
            proficiencyLevel: resource.proficiencyLevel,
            yearsOfExperience: resource.yearsOfExperience,
            certified: resource.certified,
          }],
        },
        matchScore: Math.round(matchScore),
        availability: 90, // Simplified for now
        skillMatch: Math.round(skillMatch),
        experienceMatch: Math.round(experienceMatch),
        budgetMatch: Math.round(budgetMatch),
        availabilityDetails: {
          currentAllocations: 10, // Simplified
          availableCapacity: 90,
          conflictingProjects: [],
        },
      };
    });

    // Sort by match score
    matches.sort((a, b) => b.matchScore - a.matchScore);

    res.json({
      success: true,
      data: {
        resourcePlan: {
          id: plan.id,
          role: plan.role,
          skill: { name: plan.skill_name, category: plan.skill_category },
          requirements: {
            allocationPercent: plan.allocationPercent,
            minExperience: plan.minExperience,
            maxBudget: plan.maxBudget,
            startDate: plan.startDate,
            endDate: plan.endDate,
          },
        },
        matches,
        summary: {
          totalMatches: matches.length,
          averageMatchScore: matches.length > 0
            ? Math.round(matches.reduce((sum, m) => sum + m.matchScore, 0) / matches.length)
            : 0,
          perfectMatches: matches.filter(m => m.matchScore >= 90).length,
          goodMatches: matches.filter(m => m.matchScore >= 70 && m.matchScore < 90).length,
        },
      },
    });
  } catch (error) {
    console.error('Resource matching error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Allocate Resource to Plan endpoint
app.post('/api/resource-plans/:id/allocate-resource', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { resourceId, allocationPercent, startDate, endDate } = req.body;

    // Verify resource plan exists
    const planResult = await pool.query('SELECT * FROM resource_plans WHERE id = $1', [id]);
    if (planResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource plan not found' });
    }

    const plan = planResult.rows[0];

    // Verify resource exists
    const resourceResult = await pool.query('SELECT * FROM resources WHERE id = $1', [resourceId]);
    if (resourceResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource not found' });
    }

    // Create plan allocation
    const allocationId = `pa-${Date.now()}`;
    const allocation = await pool.query(`
      INSERT INTO plan_allocations (
        id, "resourcePlanId", "resourceId", "allocationPercent",
        "startDate", "endDate", "isConfirmed", "createdAt", "updatedAt"
      )
      VALUES ($1, $2, $3, $4, $5, $6, false, NOW(), NOW())
      RETURNING *
    `, [
      allocationId, id, resourceId,
      allocationPercent || plan.allocationPercent,
      startDate || plan.startDate,
      endDate || plan.endDate
    ]);

    res.json({
      success: true,
      data: allocation.rows[0],
      message: 'Resource allocated to plan successfully',
    });
  } catch (error) {
    console.error('Resource allocation error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Resource Requests endpoints
app.get('/api/resource-requests', authenticateToken, async (req, res) => {
  try {
    const { status, priority } = req.query;
    let query = `
      SELECT rr.*, rp.role, rp."allocationPercent",
             p.name as project_name, s.name as skill_name,
             u1."firstName" as requester_firstName, u1."lastName" as requester_lastName,
             u2."firstName" as assignee_firstName, u2."lastName" as assignee_lastName
      FROM resource_requests rr
      JOIN resource_plans rp ON rr."resourcePlanId" = rp.id
      JOIN projects p ON rp."projectId" = p.id
      JOIN skills s ON rp."skillId" = s.id
      JOIN users u1 ON rr."requestedBy" = u1.id
      LEFT JOIN users u2 ON rr."assignedTo" = u2.id
    `;
    const params = [];
    const conditions = [];

    if (status) {
      conditions.push(`rr.status = $${params.length + 1}`);
      params.push(status);
    }

    if (priority) {
      conditions.push(`rr.priority = $${params.length + 1}`);
      params.push(priority);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY rr.priority DESC, rr."createdAt" DESC';

    const result = await pool.query(query, params);

    const resourceRequests = result.rows.map(row => ({
      ...row,
      resourcePlan: {
        role: row.role,
        allocationPercent: row.allocationPercent,
        project: { name: row.project_name },
        skill: { name: row.skill_name },
      },
      requester: {
        firstName: row.requester_firstName,
        lastName: row.requester_lastName,
      },
      assignee: row.assignee_firstName ? {
        firstName: row.assignee_firstName,
        lastName: row.assignee_lastName,
      } : null,
    }));

    res.json({
      success: true,
      data: resourceRequests,
      pagination: {
        page: 1,
        limit: 100,
        total: resourceRequests.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Resource requests error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create Resource Request endpoint
app.post('/api/resource-requests', authenticateToken, async (req: any, res) => {
  try {
    const {
      resourcePlanId,
      title,
      description,
      jobDescription,
      requiredSkills,
      minExperience = 0,
      maxBudget,
      priority = 'MEDIUM',
      expectedDate,
    } = req.body;

    // Verify resource plan exists
    const planResult = await pool.query(`
      SELECT rp.*, p.name as project_name, s.name as skill_name
      FROM resource_plans rp
      JOIN projects p ON rp."projectId" = p.id
      JOIN skills s ON rp."skillId" = s.id
      WHERE rp.id = $1
    `, [resourcePlanId]);

    if (planResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource plan not found' });
    }

    const plan = planResult.rows[0];
    const requestId = `rr-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO resource_requests (
        id, "resourcePlanId", title, description, "jobDescription",
        "requiredSkills", "minExperience", "maxBudget", priority,
        "requestedBy", "expectedDate", status, "createdAt", "updatedAt"
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 'OPEN', NOW(), NOW())
      RETURNING *
    `, [
      requestId, resourcePlanId, title, description, jobDescription,
      JSON.stringify(requiredSkills), minExperience, maxBudget, priority,
      req.user.id, expectedDate
    ]);

    res.json({
      success: true,
      data: {
        ...result.rows[0],
        resourcePlan: {
          role: plan.role,
          project: { name: plan.project_name },
          skill: { name: plan.skill_name },
        },
      },
      message: 'Resource request created successfully',
    });
  } catch (error) {
    console.error('Create resource request error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Update Resource Request endpoint
app.put('/api/resource-requests/:id', authenticateToken, async (req: any, res) => {
  try {
    const { id } = req.params;
    const { status, assignedTo, rejectionReason } = req.body;

    let updateFields = [];
    let params = [];
    let paramIndex = 1;

    if (status) {
      updateFields.push(`status = $${paramIndex}`);
      params.push(status);
      paramIndex++;
    }

    if (assignedTo) {
      updateFields.push(`"assignedTo" = $${paramIndex}`);
      params.push(assignedTo);
      paramIndex++;

      if (status !== 'FULFILLED') {
        updateFields.push(`status = $${paramIndex}`);
        params.push('IN_PROGRESS');
        paramIndex++;
      }
    }

    if (rejectionReason) {
      updateFields.push(`"rejectionReason" = $${paramIndex}`);
      params.push(rejectionReason);
      paramIndex++;
    }

    if (status === 'FULFILLED') {
      updateFields.push(`"fulfilledDate" = NOW()`);
    }

    updateFields.push(`"updatedAt" = NOW()`);
    params.push(id);

    const query = `
      UPDATE resource_requests
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await pool.query(query, params);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource request not found' });
    }

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Resource request updated successfully',
    });
  } catch (error) {
    console.error('Update resource request error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Comprehensive HRMS Endpoints

// Workforce Management - Get comprehensive workforce details
app.get('/api/workforce', authenticateToken, async (req: any, res) => {
  try {
    const { department, location, status, employmentType } = req.query;

    let query = `
      SELECT r.*, u."firstName", u."lastName", u.email, u.phone,
             v.name as vendor_name, v."contactPerson" as vendor_contact
      FROM resources r
      JOIN users u ON r."userId" = u.id
      LEFT JOIN vendors v ON r."vendorId" = v.id
    `;
    const params = [];
    const conditions = [];

    if (department) {
      conditions.push(`r.department ILIKE $${params.length + 1}`);
      params.push(`%${department}%`);
    }
    if (location) {
      conditions.push(`r.location ILIKE $${params.length + 1}`);
      params.push(`%${location}%`);
    }
    if (status) {
      conditions.push(`r.status = $${params.length + 1}`);
      params.push(status);
    }
    if (employmentType) {
      conditions.push(`r."employmentType" = $${params.length + 1}`);
      params.push(employmentType);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY r."createdAt" DESC';

    const result = await pool.query(query, params);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Workforce error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Workforce 360 View - Get comprehensive resource details
app.get('/api/workforce/:id', authenticateToken, async (req: any, res) => {
  try {
    const { id } = req.params;

    // Get basic resource info
    const resourceResult = await pool.query(`
      SELECT r.*, u."firstName", u."lastName", u.email, u.phone,
             v.name as vendor_name, v."contactPerson" as vendor_contact
      FROM resources r
      JOIN users u ON r."userId" = u.id
      LEFT JOIN vendors v ON r."vendorId" = v.id
      WHERE r.id = $1
    `, [id]);

    if (resourceResult.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Resource not found' });
    }

    const resource = resourceResult.rows[0];

    // Get skills
    const skillsResult = await pool.query(`
      SELECT rs.*, s.name as skill_name, s.category
      FROM resource_skills rs
      JOIN skills s ON rs."skillId" = s.id
      WHERE rs."resourceId" = $1
      ORDER BY rs."proficiencyLevel" DESC
    `, [id]);

    // Get current project allocations
    const projectsResult = await pool.query(`
      SELECT pr.*, p.name as project_name, p.status as project_status,
             p."startDate", p."endDate"
      FROM project_resources pr
      JOIN projects p ON pr."projectId" = p.id
      WHERE pr."resourceId" = $1
      ORDER BY pr."startDate" DESC
    `, [id]);

    // Get recent timesheets
    const timesheetsResult = await pool.query(`
      SELECT t.*, p.name as project_name
      FROM timesheets t
      JOIN projects p ON t."projectId" = p.id
      WHERE t."resourceId" = $1
      ORDER BY t."weekStarting" DESC
      LIMIT 10
    `, [id]);

    // Calculate utilization metrics
    const currentAllocations = projectsResult.rows
      .filter(pr => !pr.endDate || new Date(pr.endDate) >= new Date())
      .reduce((sum, pr) => sum + pr.allocationPercent, 0);

    const utilizationMetrics = {
      currentAllocation: Math.min(currentAllocations, 100),
      availableCapacity: Math.max(0, 100 - currentAllocations),
      activeProjects: projectsResult.rows.filter(pr =>
        !pr.endDate || new Date(pr.endDate) >= new Date()
      ).length,
      totalTimesheets: timesheetsResult.rows.length,
    };

    res.json({
      success: true,
      data: {
        ...resource,
        skills: skillsResult.rows,
        projects: projectsResult.rows,
        recentTimesheets: timesheetsResult.rows,
        utilizationMetrics,
      },
    });
  } catch (error) {
    console.error('Workforce 360 view error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Enhanced Vendor Management
app.get('/api/vendors/comprehensive', authenticateToken, async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT v.*, COUNT(r.id) as total_resources
      FROM vendors v
      LEFT JOIN resources r ON v.id = r."vendorId"
      GROUP BY v.id
      ORDER BY v."createdAt" DESC
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Comprehensive vendors error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Production server running on port ${PORT}`);
  console.log(`📊 Dashboard: http://localhost:3002`);
  console.log(`🔗 API: http://localhost:${PORT}`);
  console.log(`🎯 Features: Complete HRMS with Workforce Management, Resource Planning & Job Requests`);
  console.log(`👥 HRMS: Comprehensive workforce profiles, education, certifications, leaves`);
  console.log(`🏢 Vendor: Enhanced vendor onboarding with compliance tracking`);
});

export default app;
