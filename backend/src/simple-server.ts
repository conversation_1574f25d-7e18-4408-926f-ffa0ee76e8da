import express from 'express';
import cors from 'cors';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { Pool } from 'pg';

const app = express();
const PORT = process.env.PORT || 3003;

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5433/agentic_talent_pro',
});

// Middleware
app.use(cors({
  origin: ['http://localhost:3002', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  preflightContinue: false,
  optionsSuccessStatus: 204
}));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Login endpoint
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const userResult = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
    const user = userResult.rows[0];

    if (!user) {
      return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    // Check if user is active
    if (user.status !== 'ACTIVE') {
      return res.status(401).json({ success: false, error: 'Account is not active' });
    }

    // Generate token
    const jwtSecret = process.env.JWT_SECRET || 'dev-jwt-secret-key-for-development-only';
    const refreshSecret = process.env.JWT_REFRESH_SECRET || 'dev-refresh-secret-key-for-development-only';

    const token = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      jwtSecret,
      { expiresIn: '7d' }
    );

    const refreshToken = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      refreshSecret,
      { expiresIn: '30d' }
    );

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      data: {
        user: userWithoutPassword,
        token,
        refreshToken,
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Authentication middleware
const authenticateToken = async (req: any, res: any, next: any) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const token = authHeader.substring(7);
    const jwtSecret = process.env.JWT_SECRET || 'dev-jwt-secret-key-for-development-only';
    const decoded = jwt.verify(token, jwtSecret) as any;

    const userResult = await pool.query('SELECT * FROM users WHERE id = $1', [decoded.id]);
    const user = userResult.rows[0];

    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    req.user = { id: user.id, email: user.email, role: user.role };
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(401).json({ success: false, error: 'Invalid token' });
  }
};

// Get profile endpoint
app.get('/api/auth/profile', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const token = authHeader.substring(7);
    const jwtSecret = process.env.JWT_SECRET || 'dev-jwt-secret-key-for-development-only';
    const decoded = jwt.verify(token, jwtSecret) as any;

    const userResult = await pool.query('SELECT * FROM users WHERE id = $1', [decoded.id]);
    const user = userResult.rows[0];

    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    const { password: _, ...userWithoutPassword } = user;
    res.json({ success: true, data: userWithoutPassword });
  } catch (error) {
    console.error('Profile error:', error);
    res.status(401).json({ success: false, error: 'Invalid token' });
  }
});

// Dashboard stats endpoint
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    const [contractsResult, projectsResult, resourcesResult, timesheetsResult, invoicesResult] = await Promise.all([
      pool.query('SELECT COUNT(*) FROM contracts'),
      pool.query('SELECT COUNT(*) FROM projects WHERE status = $1', ['ACTIVE']),
      pool.query('SELECT COUNT(*) FROM resources WHERE status = $1', ['AVAILABLE']),
      pool.query('SELECT COUNT(*) FROM timesheets WHERE status = $1', ['SUBMITTED']),
      pool.query('SELECT COUNT(*) FROM invoices WHERE status IN ($1, $2)', ['SENT', 'OVERDUE']),
    ]);

    const stats = {
      overview: {
        totalContracts: parseInt(contractsResult.rows[0].count),
        activeProjects: parseInt(projectsResult.rows[0].count),
        totalResources: parseInt(resourcesResult.rows[0].count),
        pendingTimesheets: parseInt(timesheetsResult.rows[0].count),
        unpaidInvoices: parseInt(invoicesResult.rows[0].count),
      },
    };

    res.json({ success: true, data: stats });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Dashboard charts endpoint
app.get('/api/dashboard/charts', async (req, res) => {
  try {
    const [projectStatusResult, resourceUtilizationResult] = await Promise.all([
      pool.query('SELECT status, COUNT(*) as count FROM projects GROUP BY status'),
      pool.query('SELECT status, COUNT(*) as count FROM resources GROUP BY status'),
    ]);

    const charts = {
      projectStatus: projectStatusResult.rows,
      resourceUtilization: resourceUtilizationResult.rows,
      monthlyInvoices: [], // Placeholder for now
    };

    res.json({ success: true, data: charts });
  } catch (error) {
    console.error('Dashboard charts error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Contracts endpoint
app.get('/api/contracts', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT c.*, u."firstName", u."lastName", u.email 
      FROM contracts c 
      JOIN users u ON c."clientId" = u.id 
      ORDER BY c."createdAt" DESC
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 10,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Contracts error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Projects endpoint
app.get('/api/projects', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT p.*, c.title as contract_title, u."firstName", u."lastName"
      FROM projects p 
      JOIN contracts c ON p."contractId" = c.id
      JOIN users u ON p."managerId" = u.id 
      ORDER BY p."createdAt" DESC
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 10,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Projects error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Resources endpoint
app.get('/api/resources', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT r.*, u."firstName", u."lastName", u.email, u.phone
      FROM resources r
      JOIN users u ON r."userId" = u.id
      ORDER BY r."createdAt" DESC
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Resources error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Tasks endpoint
app.get('/api/tasks', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    const result = await pool.query(`
      SELECT t.*, p.name as project_name, u."firstName", u."lastName"
      FROM tasks t
      JOIN projects p ON t."projectId" = p.id
      LEFT JOIN users u ON t."assignedToId" = u.id
      ORDER BY t."createdAt" DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset]);

    const countResult = await pool.query('SELECT COUNT(*) FROM tasks');
    const total = parseInt(countResult.rows[0].count);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Tasks error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Timesheets endpoint
app.get('/api/timesheets', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    const result = await pool.query(`
      SELECT ts.*, p.name as project_name, u."firstName", u."lastName"
      FROM timesheets ts
      JOIN resources r ON ts."resourceId" = r.id
      JOIN users u ON r."userId" = u.id
      JOIN projects p ON ts."projectId" = p.id
      ORDER BY ts."weekEnding" DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset]);

    const countResult = await pool.query('SELECT COUNT(*) FROM timesheets');
    const total = parseInt(countResult.rows[0].count);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Timesheets error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Invoices endpoint
app.get('/api/invoices', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    const result = await pool.query(`
      SELECT i.*, p.name as project_name, c.title as contract_title
      FROM invoices i
      JOIN projects p ON i."projectId" = p.id
      JOIN contracts c ON p."contractId" = c.id
      ORDER BY i."createdAt" DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset]);

    const countResult = await pool.query('SELECT COUNT(*) FROM invoices');
    const total = parseInt(countResult.rows[0].count);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Invoices error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Vendors endpoint
app.get('/api/vendors', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT * FROM vendors
      ORDER BY "createdAt" DESC
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Vendors error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create task endpoint
app.post('/api/tasks', authenticateToken, async (req, res) => {
  try {
    const { title, description, projectId, assignedToId, priority, estimatedHours, dueDate } = req.body;
    const taskId = `task-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO tasks (id, title, description, "projectId", "assignedToId", priority, "estimatedHours", "dueDate", status, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 'TODO', NOW(), NOW())
      RETURNING *
    `, [taskId, title, description, projectId, assignedToId, priority, estimatedHours, dueDate]);

    res.json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create timesheet endpoint
app.post('/api/timesheets', authenticateToken, async (req, res) => {
  try {
    const { resourceId, projectId, weekEnding, regularHours, overtimeHours = 0 } = req.body;
    const timesheetId = `timesheet-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO timesheets (id, "resourceId", "projectId", "weekEnding", "regularHours", "overtimeHours", status, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, 'DRAFT', NOW(), NOW())
      RETURNING *
    `, [timesheetId, resourceId, projectId, weekEnding, regularHours, overtimeHours]);

    res.json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error('Create timesheet error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Users endpoint for dropdowns
app.get('/api/users', authenticateToken, async (req, res) => {
  try {
    const { role } = req.query;
    let query = 'SELECT id, "firstName", "lastName", email, role FROM users WHERE status = \'ACTIVE\'';
    const params = [];

    if (role) {
      query += ' AND role = $1';
      params.push(role);
    }

    query += ' ORDER BY "firstName", "lastName"';

    const result = await pool.query(query, params);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Users error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Project by ID endpoint
app.get('/api/projects/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(`
      SELECT p.*,
             c.title as contract_title, c.value as contract_value,
             u."firstName" as manager_firstName, u."lastName" as manager_lastName, u.email as manager_email,
             cl."firstName" as client_firstName, cl."lastName" as client_lastName, cl.email as client_email
      FROM projects p
      JOIN contracts c ON p."contractId" = c.id
      JOIN users u ON p."managerId" = u.id
      JOIN users cl ON c."clientId" = cl.id
      WHERE p.id = $1
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Project not found' });
    }

    const project = result.rows[0];

    // Get project resources
    const resourcesResult = await pool.query(`
      SELECT pr.*, r.*, u."firstName", u."lastName", u.email
      FROM project_resources pr
      JOIN resources r ON pr."resourceId" = r.id
      JOIN users u ON r."userId" = u.id
      WHERE pr."projectId" = $1
    `, [id]);

    // Get project tasks
    const tasksResult = await pool.query(`
      SELECT t.*, u."firstName", u."lastName"
      FROM tasks t
      LEFT JOIN users u ON t."assignedToId" = u.id
      WHERE t."projectId" = $1
      ORDER BY t."createdAt" DESC
    `, [id]);

    // Structure the response to match frontend expectations
    const projectData = {
      ...project,
      contract: {
        title: project.contract_title,
        value: project.contract_value,
        client: {
          firstName: project.client_firstName,
          lastName: project.client_lastName,
          email: project.client_email,
        },
      },
      manager: {
        firstName: project.manager_firstName,
        lastName: project.manager_lastName,
        email: project.manager_email,
      },
      projectResources: resourcesResult.rows.map(pr => ({
        id: pr.id,
        allocationPercent: pr.allocationPercent,
        resource: {
          user: {
            firstName: pr.firstName,
            lastName: pr.lastName,
            email: pr.email,
          },
        },
      })),
      tasks: tasksResult.rows,
    };

    res.json({
      success: true,
      data: projectData,
    });
  } catch (error) {
    console.error('Project by ID error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create project endpoint
app.post('/api/projects', authenticateToken, async (req, res) => {
  try {
    const { name, description, contractId, managerId, startDate, endDate, budget } = req.body;
    const projectId = `project-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO projects (id, name, description, "contractId", "managerId", "startDate", "endDate", budget, status, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 'PLANNING', NOW(), NOW())
      RETURNING *
    `, [projectId, name, description, contractId, managerId, startDate, endDate, budget]);

    res.json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error('Create project error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Update project endpoint
app.put('/api/projects/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, contractId, managerId, startDate, endDate, budget, status } = req.body;

    const result = await pool.query(`
      UPDATE projects
      SET name = $1, description = $2, "contractId" = $3, "managerId" = $4,
          "startDate" = $5, "endDate" = $6, budget = $7, status = $8, "updatedAt" = NOW()
      WHERE id = $9
      RETURNING *
    `, [name, description, contractId, managerId, startDate, endDate, budget, status, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, error: 'Project not found' });
    }

    res.json({
      success: true,
      data: result.rows[0],
    });
  } catch (error) {
    console.error('Update project error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Skills endpoint
app.get('/api/skills', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT * FROM skills
      ORDER BY category, name
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 100,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Skills error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Resource Plans endpoint
app.get('/api/resource-plans', authenticateToken, async (req, res) => {
  try {
    const { projectId } = req.query;
    let query = `
      SELECT rp.*, s.name as skill_name, s.category as skill_category,
             p.name as project_name
      FROM resource_plans rp
      JOIN skills s ON rp."skillId" = s.id
      JOIN projects p ON rp."projectId" = p.id
    `;
    const params = [];

    if (projectId) {
      query += ' WHERE rp."projectId" = $1';
      params.push(projectId);
    }

    query += ' ORDER BY rp."createdAt" DESC';

    const result = await pool.query(query, params);

    // Transform the data to match expected structure
    const resourcePlans = result.rows.map(row => ({
      ...row,
      skill: {
        id: row.skillId,
        name: row.skill_name,
        category: row.skill_category,
      },
      project: {
        id: row.projectId,
        name: row.project_name,
      },
      planAllocations: [], // Will be populated separately if needed
      resourceRequests: [], // Will be populated separately if needed
    }));

    res.json({
      success: true,
      data: resourcePlans,
      pagination: {
        page: 1,
        limit: 100,
        total: resourcePlans.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Resource plans error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Create Resource Plan endpoint
app.post('/api/resource-plans', authenticateToken, async (req, res) => {
  try {
    const {
      projectId,
      skillId,
      role,
      allocationPercent,
      requiredCount,
      startDate,
      endDate,
      minExperience = 0,
      maxBudget,
      description,
    } = req.body;

    const resourcePlanId = `rp-${Date.now()}`;

    const result = await pool.query(`
      INSERT INTO resource_plans (
        id, "projectId", "skillId", role, "allocationPercent", "requiredCount",
        "startDate", "endDate", "minExperience", "maxBudget", description,
        status, "createdAt", "updatedAt"
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 'DRAFT', NOW(), NOW())
      RETURNING *
    `, [
      resourcePlanId, projectId, skillId, role, allocationPercent, requiredCount,
      startDate, endDate, minExperience, maxBudget, description
    ]);

    res.json({
      success: true,
      data: result.rows[0],
      message: 'Resource plan created successfully',
    });
  } catch (error) {
    console.error('Create resource plan error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Simple server running on port ${PORT}`);
  console.log(`📊 Dashboard: http://localhost:3002`);
  console.log(`🔗 API: http://localhost:${PORT}`);
});

export default app;
