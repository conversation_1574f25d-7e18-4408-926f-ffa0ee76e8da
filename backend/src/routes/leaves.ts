import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authorize, AuthRequest } from '../middleware/auth';
import { UserRole } from '@agentic-talent-pro/shared';
import { logger } from '../utils/logger';

const router = express.Router();

/**
 * @swagger
 * /api/leaves:
 *   get:
 *     summary: Get leave records
 *     tags: [Leave Management]
 *     security:
 *       - bearerAuth: []
 */
router.get('/', authorize(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.PROJECT_MANAGER), [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['PENDING', 'APPROVED', 'REJECTED', 'CANCELLED']),
  query('type').optional().isIn(['ANNUAL', 'SICK', 'MATERNITY', 'PATERNITY', 'PERSONAL', 'EMERGENCY', 'COMPENSATORY']),
  query('resourceId').optional().isUUID(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const { status, type, resourceId } = req.query;

    const where: any = {};
    if (status) where.status = status;
    if (type) where.type = type;
    if (resourceId) where.resourceId = resourceId;

    // Project managers can only see leaves for their project resources
    if (req.user!.role === UserRole.PROJECT_MANAGER) {
      const managedProjects = await prisma.project.findMany({
        where: { managerId: req.user!.id },
        select: { id: true },
      });

      const projectIds = managedProjects.map(p => p.id);
      
      const projectResources = await prisma.projectResource.findMany({
        where: { projectId: { in: projectIds } },
        select: { resourceId: true },
      });

      const resourceIds = projectResources.map(pr => pr.resourceId);
      where.resourceId = { in: resourceIds };
    }

    const [leaves, total] = await Promise.all([
      prisma.leave.findMany({
        where,
        skip,
        take: limit,
        include: {
          resource: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          approver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.leave.count({ where }),
    ]);

    res.json({
      success: true,
      data: leaves,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/leaves:
 *   post:
 *     summary: Apply for leave
 *     tags: [Leave Management]
 *     security:
 *       - bearerAuth: []
 */
router.post('/', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  body('resourceId').isUUID(),
  body('type').isIn(['ANNUAL', 'SICK', 'MATERNITY', 'PATERNITY', 'PERSONAL', 'EMERGENCY', 'COMPENSATORY']),
  body('startDate').isISO8601(),
  body('endDate').isISO8601(),
  body('days').isFloat({ min: 0.5 }),
  body('reason').isLength({ min: 1 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { resourceId, type, startDate, endDate, days, reason } = req.body;

    // Validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start >= end) {
      throw createError('End date must be after start date', 400);
    }

    // Check for overlapping leaves
    const overlappingLeaves = await prisma.leave.findMany({
      where: {
        resourceId,
        status: { in: ['PENDING', 'APPROVED'] },
        OR: [
          {
            startDate: { lte: end },
            endDate: { gte: start },
          },
        ],
      },
    });

    if (overlappingLeaves.length > 0) {
      throw createError('Leave dates overlap with existing leave application', 400);
    }

    const leave = await prisma.leave.create({
      data: {
        resourceId,
        type,
        startDate: start,
        endDate: end,
        days,
        reason,
      },
      include: {
        resource: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
      },
    });

    logger.info('Leave application created', {
      leaveId: leave.id,
      resourceId,
      type,
      days,
      appliedBy: req.user!.id,
    });

    res.status(201).json({
      success: true,
      data: leave,
      message: 'Leave application submitted successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/leaves/{id}/approve:
 *   patch:
 *     summary: Approve leave application
 *     tags: [Leave Management]
 *     security:
 *       - bearerAuth: []
 */
router.patch('/:id/approve', authorize(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.PROJECT_MANAGER), [
  param('id').isUUID(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;

    const leave = await prisma.leave.findUnique({
      where: { id },
      include: {
        resource: {
          include: {
            projectResources: {
              include: {
                project: {
                  select: {
                    id: true,
                    managerId: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!leave) {
      throw createError('Leave application not found', 404);
    }

    if (leave.status !== 'PENDING') {
      throw createError('Only pending leave applications can be approved', 400);
    }

    // Check if user can approve this leave
    if (req.user!.role === UserRole.PROJECT_MANAGER) {
      const canApprove = leave.resource.projectResources.some(
        pr => pr.project.managerId === req.user!.id
      );
      
      if (!canApprove) {
        throw createError('You can only approve leaves for your project team members', 403);
      }
    }

    const updatedLeave = await prisma.leave.update({
      where: { id },
      data: {
        status: 'APPROVED',
        approvedBy: req.user!.id,
        approvedAt: new Date(),
      },
      include: {
        resource: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        approver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    logger.info('Leave application approved', {
      leaveId: id,
      approvedBy: req.user!.id,
    });

    res.json({
      success: true,
      data: updatedLeave,
      message: 'Leave application approved successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/leaves/{id}/reject:
 *   patch:
 *     summary: Reject leave application
 *     tags: [Leave Management]
 *     security:
 *       - bearerAuth: []
 */
router.patch('/:id/reject', authorize(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.PROJECT_MANAGER), [
  param('id').isUUID(),
  body('rejectedReason').isLength({ min: 1 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;
    const { rejectedReason } = req.body;

    const leave = await prisma.leave.findUnique({
      where: { id },
      include: {
        resource: {
          include: {
            projectResources: {
              include: {
                project: {
                  select: {
                    id: true,
                    managerId: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!leave) {
      throw createError('Leave application not found', 404);
    }

    if (leave.status !== 'PENDING') {
      throw createError('Only pending leave applications can be rejected', 400);
    }

    // Check if user can reject this leave
    if (req.user!.role === UserRole.PROJECT_MANAGER) {
      const canReject = leave.resource.projectResources.some(
        pr => pr.project.managerId === req.user!.id
      );
      
      if (!canReject) {
        throw createError('You can only reject leaves for your project team members', 403);
      }
    }

    const updatedLeave = await prisma.leave.update({
      where: { id },
      data: {
        status: 'REJECTED',
        approvedBy: req.user!.id,
        approvedAt: new Date(),
        rejectedReason,
      },
      include: {
        resource: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        approver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    logger.info('Leave application rejected', {
      leaveId: id,
      rejectedBy: req.user!.id,
      reason: rejectedReason,
    });

    res.json({
      success: true,
      data: updatedLeave,
      message: 'Leave application rejected',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
