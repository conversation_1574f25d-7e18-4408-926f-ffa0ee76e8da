import express from 'express';
import { body, param, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authorize, AuthRequest } from '../middleware/auth';
import { UserRole } from '@agentic-talent-pro/shared';
import { logger } from '../utils/logger';

const router = express.Router();

/**
 * @swagger
 * /api/education:
 *   post:
 *     summary: Add education record for a resource
 *     tags: [Education Management]
 *     security:
 *       - bearerAuth: []
 */
router.post('/', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  body('resourceId').isUUID(),
  body('degree').isLength({ min: 1 }),
  body('fieldOfStudy').isLength({ min: 1 }),
  body('institution').isLength({ min: 1 }),
  body('startYear').isInt({ min: 1950, max: new Date().getFullYear() }),
  body('endYear').optional().isInt({ min: 1950, max: new Date().getFullYear() + 10 }),
  body('percentage').optional().isFloat({ min: 0, max: 100 }),
  body('cgpa').optional().isFloat({ min: 0, max: 10 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const education = await prisma.education.create({
      data: req.body,
    });

    logger.info('Education record added', {
      educationId: education.id,
      resourceId: req.body.resourceId,
      addedBy: req.user!.id,
    });

    res.status(201).json({
      success: true,
      data: education,
      message: 'Education record added successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/education/{id}:
 *   put:
 *     summary: Update education record
 *     tags: [Education Management]
 *     security:
 *       - bearerAuth: []
 */
router.put('/:id', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  param('id').isUUID(),
  body('degree').optional().isLength({ min: 1 }),
  body('fieldOfStudy').optional().isLength({ min: 1 }),
  body('institution').optional().isLength({ min: 1 }),
  body('startYear').optional().isInt({ min: 1950, max: new Date().getFullYear() }),
  body('endYear').optional().isInt({ min: 1950, max: new Date().getFullYear() + 10 }),
  body('percentage').optional().isFloat({ min: 0, max: 100 }),
  body('cgpa').optional().isFloat({ min: 0, max: 10 }),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;

    const education = await prisma.education.update({
      where: { id },
      data: req.body,
    });

    logger.info('Education record updated', {
      educationId: id,
      updatedBy: req.user!.id,
    });

    res.json({
      success: true,
      data: education,
      message: 'Education record updated successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/education/{id}:
 *   delete:
 *     summary: Delete education record
 *     tags: [Education Management]
 *     security:
 *       - bearerAuth: []
 */
router.delete('/:id', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  param('id').isUUID(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;

    await prisma.education.delete({
      where: { id },
    });

    logger.info('Education record deleted', {
      educationId: id,
      deletedBy: req.user!.id,
    });

    res.json({
      success: true,
      message: 'Education record deleted successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/certifications:
 *   post:
 *     summary: Add certification record for a resource
 *     tags: [Certification Management]
 *     security:
 *       - bearerAuth: []
 */
router.post('/certifications', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  body('resourceId').isUUID(),
  body('name').isLength({ min: 1 }),
  body('issuingOrg').isLength({ min: 1 }),
  body('issueDate').isISO8601(),
  body('expiryDate').optional().isISO8601(),
  body('credentialId').optional().isLength({ min: 1 }),
  body('credentialUrl').optional().isURL(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const certificationData = {
      ...req.body,
      issueDate: new Date(req.body.issueDate),
      expiryDate: req.body.expiryDate ? new Date(req.body.expiryDate) : undefined,
    };

    const certification = await prisma.certification.create({
      data: certificationData,
    });

    logger.info('Certification record added', {
      certificationId: certification.id,
      resourceId: req.body.resourceId,
      addedBy: req.user!.id,
    });

    res.status(201).json({
      success: true,
      data: certification,
      message: 'Certification record added successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/certifications/{id}:
 *   put:
 *     summary: Update certification record
 *     tags: [Certification Management]
 *     security:
 *       - bearerAuth: []
 */
router.put('/certifications/:id', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  param('id').isUUID(),
  body('name').optional().isLength({ min: 1 }),
  body('issuingOrg').optional().isLength({ min: 1 }),
  body('issueDate').optional().isISO8601(),
  body('expiryDate').optional().isISO8601(),
  body('verified').optional().isBoolean(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;
    const updateData = { ...req.body };

    if (updateData.issueDate) {
      updateData.issueDate = new Date(updateData.issueDate);
    }
    if (updateData.expiryDate) {
      updateData.expiryDate = new Date(updateData.expiryDate);
    }

    const certification = await prisma.certification.update({
      where: { id },
      data: updateData,
    });

    logger.info('Certification record updated', {
      certificationId: id,
      updatedBy: req.user!.id,
    });

    res.json({
      success: true,
      data: certification,
      message: 'Certification record updated successfully',
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/languages:
 *   post:
 *     summary: Add language proficiency record for a resource
 *     tags: [Language Management]
 *     security:
 *       - bearerAuth: []
 */
router.post('/languages', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  body('resourceId').isUUID(),
  body('language').isLength({ min: 1 }),
  body('speaking').isIn(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'NATIVE']),
  body('reading').isIn(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'NATIVE']),
  body('writing').isIn(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'NATIVE']),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const language = await prisma.languageProficiency.create({
      data: req.body,
    });

    logger.info('Language proficiency record added', {
      languageId: language.id,
      resourceId: req.body.resourceId,
      addedBy: req.user!.id,
    });

    res.status(201).json({
      success: true,
      data: language,
      message: 'Language proficiency record added successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
