import express from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authorize, AuthRequest } from '../middleware/auth';
import { UserRole } from '@agentic-talent-pro/shared';
import { logger } from '../utils/logger';

const router = express.Router();

/**
 * @swagger
 * /api/workforce:
 *   get:
 *     summary: Get all workforce members with comprehensive details
 *     tags: [Workforce Management]
 *     security:
 *       - bearerAuth: []
 */
router.get('/', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('department').optional().isString(),
  query('location').optional().isString(),
  query('status').optional().isIn(['AVAILABLE', 'ALLOCATED', 'ON_LEAVE', 'TERMINATED']),
  query('employmentType').optional().isIn(['FTE', 'CONTRACTOR', 'VENDOR']),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const { department, location, status, employmentType } = req.query;

    const where: any = {};
    if (department) where.department = { contains: department, mode: 'insensitive' };
    if (location) where.location = { contains: location, mode: 'insensitive' };
    if (status) where.status = status;
    if (employmentType) where.employmentType = employmentType;

    const [workforce, total] = await Promise.all([
      prisma.resource.findMany({
        where,
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              phone: true,
            },
          },
          skills: {
            include: {
              skill: true,
            },
          },
          educations: true,
          certifications: true,
          languages: true,
          vendor: {
            select: {
              id: true,
              name: true,
              contactPerson: true,
            },
          },
          projectResources: {
            where: {
              OR: [
                { endDate: { gte: new Date() } },
                { endDate: null },
              ],
            },
            include: {
              project: {
                select: {
                  id: true,
                  name: true,
                  status: true,
                },
              },
            },
          },
          _count: {
            select: {
              timesheets: true,
              leaves: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.resource.count({ where }),
    ]);

    res.json({
      success: true,
      data: workforce,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/workforce/{id}:
 *   get:
 *     summary: Get comprehensive workforce member details (360 view)
 *     tags: [Workforce Management]
 *     security:
 *       - bearerAuth: []
 */
router.get('/:id', authorize(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.PROJECT_MANAGER), [
  param('id').isUUID(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;

    const workforce = await prisma.resource.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
            avatar: true,
            createdAt: true,
          },
        },
        skills: {
          include: {
            skill: true,
          },
          orderBy: { proficiencyLevel: 'desc' },
        },
        educations: {
          orderBy: { endYear: 'desc' },
        },
        certifications: {
          orderBy: { issueDate: 'desc' },
        },
        languages: {
          orderBy: { language: 'asc' },
        },
        vendor: true,
        projectResources: {
          include: {
            project: {
              select: {
                id: true,
                name: true,
                status: true,
                startDate: true,
                endDate: true,
                manager: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
          orderBy: { startDate: 'desc' },
        },
        planAllocations: {
          include: {
            resourcePlan: {
              include: {
                project: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
                skill: true,
              },
            },
          },
        },
        timesheets: {
          where: {
            weekStarting: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth() - 3, 1), // Last 3 months
            },
          },
          include: {
            project: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: { weekStarting: 'desc' },
        },
        leaves: {
          where: {
            startDate: {
              gte: new Date(new Date().getFullYear(), 0, 1), // Current year
            },
          },
          orderBy: { startDate: 'desc' },
        },
      },
    });

    if (!workforce) {
      throw createError('Workforce member not found', 404);
    }

    // Calculate utilization metrics
    const currentAllocations = workforce.projectResources
      .filter(pr => !pr.endDate || pr.endDate >= new Date())
      .reduce((sum, pr) => sum + pr.allocationPercent, 0);

    const utilizationMetrics = {
      currentAllocation: Math.min(currentAllocations, 100),
      availableCapacity: Math.max(0, 100 - currentAllocations),
      activeProjects: workforce.projectResources.filter(pr => 
        !pr.endDate || pr.endDate >= new Date()
      ).length,
      totalTimesheets: workforce._count?.timesheets || 0,
      totalLeaves: workforce._count?.leaves || 0,
    };

    res.json({
      success: true,
      data: {
        ...workforce,
        utilizationMetrics,
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/workforce/{id}/profile:
 *   put:
 *     summary: Update workforce member comprehensive profile
 *     tags: [Workforce Management]
 *     security:
 *       - bearerAuth: []
 */
router.put('/:id/profile', authorize(UserRole.ADMIN, UserRole.HR_MANAGER), [
  param('id').isUUID(),
  body('personalInfo').optional().isObject(),
  body('professionalInfo').optional().isObject(),
  body('financialInfo').optional().isObject(),
  body('complianceInfo').optional().isObject(),
], async (req: AuthRequest, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createError('Validation failed', 400);
    }

    const { id } = req.params;
    const { personalInfo, professionalInfo, financialInfo, complianceInfo } = req.body;

    // Verify resource exists
    const existingResource = await prisma.resource.findUnique({
      where: { id },
    });

    if (!existingResource) {
      throw createError('Workforce member not found', 404);
    }

    // Build update data
    const updateData: any = {};

    if (personalInfo) {
      Object.assign(updateData, {
        dateOfBirth: personalInfo.dateOfBirth ? new Date(personalInfo.dateOfBirth) : undefined,
        gender: personalInfo.gender,
        maritalStatus: personalInfo.maritalStatus,
        nationality: personalInfo.nationality,
        emergencyContact: personalInfo.emergencyContact ? JSON.stringify(personalInfo.emergencyContact) : undefined,
      });
    }

    if (professionalInfo) {
      Object.assign(updateData, {
        totalExperience: professionalInfo.totalExperience,
        previousCompany: professionalInfo.previousCompany,
        reportingManager: professionalInfo.reportingManager,
        workLocation: professionalInfo.workLocation,
      });
    }

    if (financialInfo) {
      Object.assign(updateData, {
        panNumber: financialInfo.panNumber,
        bankDetails: financialInfo.bankDetails ? JSON.stringify(financialInfo.bankDetails) : undefined,
        salary: financialInfo.salary,
      });
    }

    if (complianceInfo) {
      Object.assign(updateData, {
        backgroundCheck: complianceInfo.backgroundCheck,
        backgroundCheckDate: complianceInfo.backgroundCheckDate ? new Date(complianceInfo.backgroundCheckDate) : undefined,
        securityClearance: complianceInfo.securityClearance,
        clearanceExpiry: complianceInfo.clearanceExpiry ? new Date(complianceInfo.clearanceExpiry) : undefined,
        entryPass: complianceInfo.entryPass,
        documentsVerified: complianceInfo.documentsVerified,
        aadharNumber: complianceInfo.aadharNumber,
        passportNumber: complianceInfo.passportNumber,
        drivingLicense: complianceInfo.drivingLicense,
      });
    }

    const updatedResource = await prisma.resource.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    logger.info('Workforce profile updated', {
      resourceId: id,
      updatedBy: req.user!.id,
      sections: Object.keys(req.body),
    });

    res.json({
      success: true,
      data: updatedResource,
      message: 'Workforce profile updated successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
