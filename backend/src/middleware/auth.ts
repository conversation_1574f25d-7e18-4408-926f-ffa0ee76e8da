import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { prisma } from '../config/database';
import { createError } from './errorHandler';
import { UserRole } from '@agentic-talent-pro/shared';

export interface AuthRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: UserRole;
  };
}

export const authenticate = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw createError('Access token required', 401);
    }

    const token = authHeader.substring(7);

    const jwtSecret = process.env.JWT_SECRET || 'dev-jwt-secret-key-for-development-only';

    const decoded = jwt.verify(token, jwtSecret) as {
      id: string;
      email: string;
      role: UserRole;
    };

    // Verify user still exists and is active
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      select: { id: true, email: true, role: true, status: true },
    });

    if (!user) {
      throw createError('User not found', 401);
    }

    if (user.status !== 'ACTIVE') {
      throw createError('Account is not active', 401);
    }

    req.user = {
      id: user.id,
      email: user.email,
      role: user.role as UserRole,
    };

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(createError('Invalid token', 401));
    } else if (error instanceof jwt.TokenExpiredError) {
      next(createError('Token expired', 401));
    } else {
      next(error);
    }
  }
};

export const authorize = (...roles: UserRole[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(createError('Authentication required', 401));
    }

    if (!roles.includes(req.user.role)) {
      return next(createError('Insufficient permissions', 403));
    }

    next();
  };
};

// Check if user can access specific resource
export const checkResourceAccess = (resourceField: string = 'id') => {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return next(createError('Authentication required', 401));
      }

      // Admin can access everything
      if (req.user.role === UserRole.ADMIN) {
        return next();
      }

      const resourceId = req.params[resourceField];
      
      // For resources, check if user owns the resource or is a manager
      if (req.baseUrl.includes('/resources')) {
        const resource = await prisma.resource.findUnique({
          where: { id: resourceId },
          include: { user: true },
        });

        if (!resource) {
          return next(createError('Resource not found', 404));
        }

        // User can access their own resource
        if (resource.userId === req.user.id) {
          return next();
        }

        // HR managers can access all resources
        if (req.user.role === UserRole.HR_MANAGER) {
          return next();
        }

        return next(createError('Access denied', 403));
      }

      // For projects, check if user is manager or assigned to project
      if (req.baseUrl.includes('/projects')) {
        const project = await prisma.project.findUnique({
          where: { id: resourceId },
          include: {
            projectResources: {
              include: { resource: true },
            },
          },
        });

        if (!project) {
          return next(createError('Project not found', 404));
        }

        // Project manager can access
        if (project.managerId === req.user.id) {
          return next();
        }

        // Assigned resources can access
        const isAssigned = project.projectResources.some(
          (pr: any) => pr.resource.userId === req.user!.id
        );

        if (isAssigned) {
          return next();
        }

        return next(createError('Access denied', 403));
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};
