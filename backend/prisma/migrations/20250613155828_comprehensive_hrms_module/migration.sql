-- Create<PERSON><PERSON>
CREATE TYPE "ProficiencyLevel" AS ENUM ('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'NATIVE');

-- CreateEnum
CREATE TYPE "LeaveType" AS ENUM ('ANNUAL', 'SICK', 'MATERNITY', 'PATERNITY', 'PERSONA<PERSON>', 'EMERGENCY', 'COMPENSATORY');

-- <PERSON>reate<PERSON>num
CREATE TYPE "LeaveStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED', 'CANCELLED');

-- AlterTable
ALTER TABLE "resources" ADD COLUMN     "aadharNumber" TEXT,
ADD COLUMN     "backgroundCheckDate" TIMESTAMP(3),
ADD COLUMN     "clearanceExpiry" TIMESTAMP(3),
ADD COLUMN     "confirmationDate" TIMESTAMP(3),
ADD COLUMN     "dateOfBirth" TIMESTAMP(3),
ADD COLUMN     "documentsVerified" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "drivingLicense" TEXT,
ADD COLUMN     "emergencyContact" TEXT,
ADD COLUMN     "entryPass" TEXT,
ADD COLUMN     "gender" TEXT,
ADD COLUMN     "maritalStatus" TEXT,
ADD COLUMN     "nationality" TEXT,
ADD COLUMN     "passportNumber" TEXT,
ADD COLUMN     "previousCompany" TEXT,
ADD COLUMN     "reportingManager" TEXT,
ADD COLUMN     "salary" DOUBLE PRECISION,
ADD COLUMN     "totalExperience" DOUBLE PRECISION,
ADD COLUMN     "workLocation" TEXT;

-- AlterTable
ALTER TABLE "vendors" ADD COLUMN     "activeContracts" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "alternatePhone" TEXT,
ADD COLUMN     "city" TEXT,
ADD COLUMN     "cmmiLevel" TEXT,
ADD COLUMN     "companyType" TEXT,
ADD COLUMN     "contractType" TEXT,
ADD COLUMN     "country" TEXT,
ADD COLUMN     "currency" TEXT NOT NULL DEFAULT 'USD',
ADD COLUMN     "incorporationDate" TIMESTAMP(3),
ADD COLUMN     "iso27001" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "iso9001" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "msmeCertificate" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "onboardedAt" TIMESTAMP(3),
ADD COLUMN     "onboardedBy" TEXT,
ADD COLUMN     "paymentTerms" TEXT,
ADD COLUMN     "pincode" TEXT,
ADD COLUMN     "rating" DOUBLE PRECISION,
ADD COLUMN     "registrationNumber" TEXT,
ADD COLUMN     "state" TEXT,
ADD COLUMN     "tanNumber" TEXT,
ADD COLUMN     "totalResources" INTEGER NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "educations" (
    "id" TEXT NOT NULL,
    "resourceId" TEXT NOT NULL,
    "degree" TEXT NOT NULL,
    "fieldOfStudy" TEXT NOT NULL,
    "institution" TEXT NOT NULL,
    "university" TEXT,
    "startYear" INTEGER NOT NULL,
    "endYear" INTEGER,
    "percentage" DOUBLE PRECISION,
    "cgpa" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "educations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "certifications" (
    "id" TEXT NOT NULL,
    "resourceId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "issuingOrg" TEXT NOT NULL,
    "issueDate" TIMESTAMP(3) NOT NULL,
    "expiryDate" TIMESTAMP(3),
    "credentialId" TEXT,
    "credentialUrl" TEXT,
    "verified" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "certifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "language_proficiencies" (
    "id" TEXT NOT NULL,
    "resourceId" TEXT NOT NULL,
    "language" TEXT NOT NULL,
    "speaking" "ProficiencyLevel" NOT NULL,
    "reading" "ProficiencyLevel" NOT NULL,
    "writing" "ProficiencyLevel" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "language_proficiencies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "leaves" (
    "id" TEXT NOT NULL,
    "resourceId" TEXT NOT NULL,
    "type" "LeaveType" NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "days" DOUBLE PRECISION NOT NULL,
    "reason" TEXT NOT NULL,
    "status" "LeaveStatus" NOT NULL DEFAULT 'PENDING',
    "approvedBy" TEXT,
    "approvedAt" TIMESTAMP(3),
    "rejectedReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "leaves_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "educations" ADD CONSTRAINT "educations_resourceId_fkey" FOREIGN KEY ("resourceId") REFERENCES "resources"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "certifications" ADD CONSTRAINT "certifications_resourceId_fkey" FOREIGN KEY ("resourceId") REFERENCES "resources"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "language_proficiencies" ADD CONSTRAINT "language_proficiencies_resourceId_fkey" FOREIGN KEY ("resourceId") REFERENCES "resources"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leaves" ADD CONSTRAINT "leaves_resourceId_fkey" FOREIGN KEY ("resourceId") REFERENCES "resources"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "leaves" ADD CONSTRAINT "leaves_approvedBy_fkey" FOREIGN KEY ("approvedBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "vendors" ADD CONSTRAINT "vendors_onboardedBy_fkey" FOREIGN KEY ("onboardedBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
