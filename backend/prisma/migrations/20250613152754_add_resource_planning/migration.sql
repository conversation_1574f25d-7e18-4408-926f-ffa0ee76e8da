/*
  Warnings:

  - Added the required column `endDate` to the `resource_plans` table without a default value. This is not possible if the table is not empty.
  - Added the required column `startDate` to the `resource_plans` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "ResourcePlanStatus" AS ENUM ('DRAFT', 'ACTIVE', 'FULFILLED', 'CANCELLED');

-- <PERSON>reateEnum
CREATE TYPE "ResourceRequestStatus" AS ENUM ('OPEN', 'IN_PROGRESS', 'FULFILLED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "ResourceRequestPriority" AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'URGENT');

-- AlterTable
ALTER TABLE "resource_plans" ADD COLUMN     "endDate" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "maxBudget" DOUBLE PRECISION,
ADD COLUMN     "minExperience" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "startDate" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "status" "ResourcePlanStatus" NOT NULL DEFAULT 'DRAFT';

-- CreateTable
CREATE TABLE "resource_requests" (
    "id" TEXT NOT NULL,
    "resourcePlanId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "jobDescription" TEXT NOT NULL,
    "requiredSkills" TEXT NOT NULL,
    "minExperience" INTEGER NOT NULL DEFAULT 0,
    "maxBudget" DOUBLE PRECISION,
    "priority" "ResourceRequestPriority" NOT NULL DEFAULT 'MEDIUM',
    "status" "ResourceRequestStatus" NOT NULL DEFAULT 'OPEN',
    "requestedBy" TEXT NOT NULL,
    "assignedTo" TEXT,
    "expectedDate" TIMESTAMP(3),
    "fulfilledDate" TIMESTAMP(3),
    "rejectionReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "resource_requests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "plan_allocations" (
    "id" TEXT NOT NULL,
    "resourcePlanId" TEXT NOT NULL,
    "resourceId" TEXT NOT NULL,
    "allocationPercent" INTEGER NOT NULL DEFAULT 100,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3),
    "isConfirmed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "plan_allocations_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "plan_allocations_resourcePlanId_resourceId_key" ON "plan_allocations"("resourcePlanId", "resourceId");

-- AddForeignKey
ALTER TABLE "resource_requests" ADD CONSTRAINT "resource_requests_resourcePlanId_fkey" FOREIGN KEY ("resourcePlanId") REFERENCES "resource_plans"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "resource_requests" ADD CONSTRAINT "resource_requests_requestedBy_fkey" FOREIGN KEY ("requestedBy") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "resource_requests" ADD CONSTRAINT "resource_requests_assignedTo_fkey" FOREIGN KEY ("assignedTo") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "plan_allocations" ADD CONSTRAINT "plan_allocations_resourcePlanId_fkey" FOREIGN KEY ("resourcePlanId") REFERENCES "resource_plans"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "plan_allocations" ADD CONSTRAINT "plan_allocations_resourceId_fkey" FOREIGN KEY ("resourceId") REFERENCES "resources"("id") ON DELETE CASCADE ON UPDATE CASCADE;
