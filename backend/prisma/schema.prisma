// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  PROJECT_MANAGER
  RESOURCE
  CLIENT
  HR_MANAGER
  BILLING_MANAGER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  PENDING
}

model User {
  id        String     @id @default(cuid())
  email     String     @unique
  password  String
  firstName String
  lastName  String
  role      UserRole
  status    UserStatus @default(PENDING)
  phone     String?
  avatar    String?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  // Relations
  resource         Resource?
  managedProjects  Project[]
  assignedTasks    Task[]
  approvedTimesheets Timesheet[] @relation("ApprovedBy")
  createdContracts Contract[]
  requestedResourceRequests ResourceRequest[] @relation("RequestedBy")
  assignedResourceRequests  ResourceRequest[] @relation("AssignedTo")
  approvedLeaves   Leave[]     @relation("LeaveApprover")
  onboardedVendors Vendor[]    @relation("VendorOnboarder")

  @@map("users")
}

enum ContractStatus {
  DRAFT
  ACTIVE
  COMPLETED
  TERMINATED
}

enum ContractType {
  FIXED_PRICE
  TIME_AND_MATERIAL
  RETAINER
}

model Contract {
  id          String         @id @default(cuid())
  title       String
  description String
  clientId    String
  type        ContractType
  status      ContractStatus @default(DRAFT)
  startDate   DateTime
  endDate     DateTime
  value       Float
  currency    String         @default("USD")
  terms       String
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Relations
  client   User      @relation(fields: [clientId], references: [id])
  projects Project[]

  @@map("contracts")
}

enum ProjectStatus {
  PLANNING
  ACTIVE
  ON_HOLD
  COMPLETED
  CANCELLED
}

model Project {
  id          String        @id @default(cuid())
  name        String
  description String
  contractId  String
  managerId   String
  status      ProjectStatus @default(PLANNING)
  startDate   DateTime
  endDate     DateTime
  budget      Float
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  contract         Contract           @relation(fields: [contractId], references: [id])
  manager          User               @relation(fields: [managerId], references: [id])
  tasks            Task[]
  timesheets       Timesheet[]
  invoices         Invoice[]
  resourcePlans    ResourcePlan[]
  projectResources ProjectResource[]

  @@map("projects")
}

enum EmploymentType {
  FULL_TIME
  CONTRACTOR
  VENDOR
}

enum ResourceStatus {
  AVAILABLE
  ALLOCATED
  ON_LEAVE
  TERMINATED
}

model Resource {
  id                String           @id @default(cuid())
  userId            String           @unique
  employeeId        String           @unique
  employmentType    EmploymentType
  status            ResourceStatus   @default(AVAILABLE)
  designation       String
  department        String
  location          String
  hourlyRate        Float
  joiningDate       DateTime
  confirmationDate  DateTime?

  // Personal Information
  dateOfBirth       DateTime?
  gender            String?
  maritalStatus     String?
  nationality       String?
  emergencyContact  String? // JSON: {name, relationship, phone, email}

  // Professional Information
  totalExperience   Float?           // Years
  previousCompany   String?
  reportingManager  String?
  workLocation      String?

  // Financial Information
  panNumber         String?
  bankDetails       String?          // JSON: {bankName, accountNumber, ifscCode, accountType}
  salary            Float?

  // Compliance & Security
  backgroundCheck   Boolean          @default(false)
  backgroundCheckDate DateTime?
  securityClearance String?          // SC_CLEARED, SECRET, TOP_SECRET, etc.
  clearanceExpiry   DateTime?
  entryPass         String?          // Location-specific entry permissions

  // Documents & Verification
  documentsVerified Boolean          @default(false)
  aadharNumber      String?
  passportNumber    String?
  drivingLicense    String?

  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  // Relations
  user             User               @relation(fields: [userId], references: [id])
  skills           ResourceSkill[]
  timesheets       Timesheet[]
  invoices         Invoice[]
  projectResources ProjectResource[]
  planAllocations  PlanAllocation[]
  vendorId         String?
  vendor           Vendor?            @relation(fields: [vendorId], references: [id])
  educations       Education[]
  certifications   Certification[]
  languages        LanguageProficiency[]
  leaves           Leave[]

  @@map("resources")
}

model Skill {
  id          String @id @default(cuid())
  name        String @unique
  category    String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  resourceSkills ResourceSkill[]
  resourcePlans  ResourcePlan[]

  @@map("skills")
}

model ResourceSkill {
  resourceId        String
  skillId           String
  proficiencyLevel  Int     @default(1) // 1-5 scale
  yearsOfExperience Int     @default(0)
  certified         Boolean @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  resource Resource @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  skill    Skill    @relation(fields: [skillId], references: [id], onDelete: Cascade)

  @@id([resourceId, skillId])
  @@map("resource_skills")
}

// Education Information
model Education {
  id           String   @id @default(cuid())
  resourceId   String
  degree       String   // Bachelor's, Master's, PhD, Diploma, etc.
  fieldOfStudy String
  institution  String
  university   String?
  startYear    Int
  endYear      Int?
  percentage   Float?
  cgpa         Float?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  resource Resource @relation(fields: [resourceId], references: [id], onDelete: Cascade)

  @@map("educations")
}

// Certification Information
model Certification {
  id             String    @id @default(cuid())
  resourceId     String
  name           String
  issuingOrg     String
  issueDate      DateTime
  expiryDate     DateTime?
  credentialId   String?
  credentialUrl  String?
  verified       Boolean   @default(false)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // Relations
  resource Resource @relation(fields: [resourceId], references: [id], onDelete: Cascade)

  @@map("certifications")
}

// Language Proficiency
enum ProficiencyLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  NATIVE
}

model LanguageProficiency {
  id           String           @id @default(cuid())
  resourceId   String
  language     String
  speaking     ProficiencyLevel
  reading      ProficiencyLevel
  writing      ProficiencyLevel
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  // Relations
  resource Resource @relation(fields: [resourceId], references: [id], onDelete: Cascade)

  @@map("language_proficiencies")
}

// Leave Management
enum LeaveType {
  ANNUAL
  SICK
  MATERNITY
  PATERNITY
  PERSONAL
  EMERGENCY
  COMPENSATORY
}

enum LeaveStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

model Leave {
  id          String      @id @default(cuid())
  resourceId  String
  type        LeaveType
  startDate   DateTime
  endDate     DateTime
  days        Float       // Can be fractional for half days
  reason      String
  status      LeaveStatus @default(PENDING)
  approvedBy  String?
  approvedAt  DateTime?
  rejectedReason String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  resource Resource @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  approver User?    @relation("LeaveApprover", fields: [approvedBy], references: [id])

  @@map("leaves")
}

enum ResourcePlanStatus {
  DRAFT
  ACTIVE
  FULFILLED
  CANCELLED
}

enum ResourceRequestStatus {
  OPEN
  IN_PROGRESS
  FULFILLED
  CANCELLED
}

enum ResourceRequestPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

model ResourcePlan {
  id                String             @id @default(cuid())
  projectId         String
  skillId           String
  role              String
  allocationPercent Int                @default(100)
  requiredCount     Int                @default(1)
  startDate         DateTime
  endDate           DateTime
  minExperience     Int                @default(0) // Years of experience required
  maxBudget         Float?             // Maximum budget per resource
  description       String?
  status            ResourcePlanStatus @default(DRAFT)
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt

  // Relations
  project          Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  skill            Skill             @relation(fields: [skillId], references: [id])
  resourceRequests ResourceRequest[]
  planAllocations  PlanAllocation[]

  @@map("resource_plans")
}

model ResourceRequest {
  id               String                  @id @default(cuid())
  resourcePlanId   String
  title            String
  description      String
  jobDescription   String
  requiredSkills   String // JSON array of skill requirements
  minExperience    Int                     @default(0)
  maxBudget        Float?
  priority         ResourceRequestPriority @default(MEDIUM)
  status           ResourceRequestStatus   @default(OPEN)
  requestedBy      String
  assignedTo       String? // HR Manager assigned to fulfill this request
  expectedDate     DateTime?
  fulfilledDate    DateTime?
  rejectionReason  String?
  createdAt        DateTime                @default(now())
  updatedAt        DateTime                @updatedAt

  // Relations
  resourcePlan ResourcePlan @relation(fields: [resourcePlanId], references: [id], onDelete: Cascade)
  requester    User         @relation("RequestedBy", fields: [requestedBy], references: [id])
  assignee     User?        @relation("AssignedTo", fields: [assignedTo], references: [id])

  @@map("resource_requests")
}

model PlanAllocation {
  id             String   @id @default(cuid())
  resourcePlanId String
  resourceId     String
  allocationPercent Int   @default(100)
  startDate      DateTime
  endDate        DateTime?
  isConfirmed    Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  resourcePlan ResourcePlan @relation(fields: [resourcePlanId], references: [id], onDelete: Cascade)
  resource     Resource     @relation(fields: [resourceId], references: [id], onDelete: Cascade)

  @@unique([resourcePlanId, resourceId])
  @@map("plan_allocations")
}

model ProjectResource {
  id               String   @id @default(cuid())
  projectId        String
  resourceId       String
  allocationPercent Int     @default(100)
  startDate        DateTime
  endDate          DateTime?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  project  Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  resource Resource @relation(fields: [resourceId], references: [id], onDelete: Cascade)

  @@unique([projectId, resourceId])
  @@map("project_resources")
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  REVIEW
  COMPLETED
  BLOCKED
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

model Task {
  id             String       @id @default(cuid())
  title          String
  description    String
  projectId      String
  assignedToId   String
  status         TaskStatus   @default(TODO)
  priority       TaskPriority @default(MEDIUM)
  estimatedHours Float
  actualHours    Float        @default(0)
  startDate      DateTime
  dueDate        DateTime
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  // Relations
  project      Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  assignedTo   User              @relation(fields: [assignedToId], references: [id])
  timesheetEntries TimesheetEntry[]

  @@map("tasks")
}

enum TimesheetStatus {
  DRAFT
  SUBMITTED
  APPROVED
  REJECTED
}

model Timesheet {
  id           String          @id @default(cuid())
  resourceId   String
  projectId    String
  weekStarting DateTime
  weekEnding   DateTime
  status       TimesheetStatus @default(DRAFT)
  totalHours   Float           @default(0)
  submittedAt  DateTime?
  approvedAt   DateTime?
  approvedBy   String?
  rejectionReason String?
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt

  // Relations
  resource  Resource         @relation(fields: [resourceId], references: [id])
  project   Project          @relation(fields: [projectId], references: [id])
  approver  User?            @relation("ApprovedBy", fields: [approvedBy], references: [id])
  entries   TimesheetEntry[]
  invoices  Invoice[]

  @@unique([resourceId, projectId, weekStarting])
  @@map("timesheets")
}

model TimesheetEntry {
  id          String   @id @default(cuid())
  timesheetId String
  taskId      String
  date        DateTime
  hours       Float
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  timesheet Timesheet @relation(fields: [timesheetId], references: [id], onDelete: Cascade)
  task      Task      @relation(fields: [taskId], references: [id])

  @@map("timesheet_entries")
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

model Invoice {
  id            String        @id @default(cuid())
  invoiceNumber String        @unique
  projectId     String
  resourceId    String
  timesheetId   String
  status        InvoiceStatus @default(DRAFT)
  issueDate     DateTime
  dueDate       DateTime
  subtotal      Float
  tax           Float
  penalties     Float         @default(0)
  total         Float
  paidAt        DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  project   Project   @relation(fields: [projectId], references: [id])
  resource  Resource  @relation(fields: [resourceId], references: [id])
  timesheet Timesheet @relation(fields: [timesheetId], references: [id])

  @@map("invoices")
}

enum VendorStatus {
  ACTIVE
  INACTIVE
  BLACKLISTED
}

model Vendor {
  id            String       @id @default(cuid())
  name          String
  contactPerson String
  email         String       @unique
  phone         String
  alternatePhone String?

  // Address Information
  address       String
  city          String?
  state         String?
  country       String?
  pincode       String?

  // Company Information
  companyType   String?      // Private Limited, Partnership, Proprietorship, etc.
  incorporationDate DateTime?
  registrationNumber String?
  gstNumber     String?
  panNumber     String
  tanNumber     String?

  // Banking Information
  bankDetails   String       // JSON: {bankName, accountNumber, ifscCode, accountType, branch}

  // Compliance & Verification
  msmeCertificate Boolean    @default(false)
  iso27001      Boolean      @default(false)
  iso9001       Boolean      @default(false)
  cmmiLevel     String?      // CMMI Level 1-5

  // Contract & Commercial
  contractType  String?      // Master Service Agreement, SOW, etc.
  paymentTerms  String?      // NET30, NET45, etc.
  currency      String       @default("USD")

  // Performance Metrics
  rating        Float?       // 1-5 rating
  totalResources Int         @default(0)
  activeContracts Int        @default(0)

  status        VendorStatus @default(ACTIVE)
  onboardedBy   String?
  onboardedAt   DateTime?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  // Relations
  resources Resource[]
  onboarder User?      @relation("VendorOnboarder", fields: [onboardedBy], references: [id])

  @@map("vendors")
}
