-- Seed data for Agentic Talent Pro
-- Insert Users
INSERT INTO users (id, email, password, "firstName", "lastName", role, status, phone, "createdAt", "updatedAt") VALUES
('admin-1', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'System', 'Administrator', 'ADMIN', 'ACTIVE', '+1234567890', NOW(), NOW()),
('pm-1', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'John', 'Manager', 'PROJECT_MANAGER', 'ACTIVE', '+1234567891', NOW(), NOW()),
('hr-1', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', '<PERSON>', 'HR', 'HR_MANAGER', 'ACTIVE', '+1234567892', NOW(), NOW()),
('billing-1', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Mike', 'Finance', 'BILLING_MANAGER', 'ACTIVE', '+1234567893', NOW(), NOW()),
('client-1', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Jane', 'Client', 'CLIENT', 'ACTIVE', '+1234567894', NOW(), NOW()),
('resource-1', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Alice', 'Developer', 'RESOURCE', 'ACTIVE', '+1234567895', NOW(), NOW()),
('resource-2', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'Bob', 'Designer', 'RESOURCE', 'ACTIVE', '+1234567896', NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

-- Insert Skills
INSERT INTO skills (id, name, category, description, "createdAt", "updatedAt") VALUES
('skill-1', 'JavaScript', 'Programming', 'JavaScript programming language', NOW(), NOW()),
('skill-2', 'TypeScript', 'Programming', 'TypeScript programming language', NOW(), NOW()),
('skill-3', 'React', 'Frontend', 'React.js framework', NOW(), NOW()),
('skill-4', 'Node.js', 'Backend', 'Node.js runtime', NOW(), NOW()),
('skill-5', 'PostgreSQL', 'Database', 'PostgreSQL database', NOW(), NOW()),
('skill-6', 'UI/UX Design', 'Design', 'User interface and experience design', NOW(), NOW()),
('skill-7', 'Project Management', 'Management', 'Project management skills', NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- Insert Resources
INSERT INTO resources (id, "userId", "employeeId", "employmentType", status, designation, department, location, "hourlyRate", "joiningDate", "panNumber", "backgroundCheck", "createdAt", "updatedAt") VALUES
('res-1', 'resource-1', 'EMP001', 'FULL_TIME', 'AVAILABLE', 'Senior Developer', 'Engineering', 'New York', 75.0, '2023-01-15', '**********', true, NOW(), NOW()),
('res-2', 'resource-2', 'EMP002', 'CONTRACTOR', 'AVAILABLE', 'UI/UX Designer', 'Design', 'San Francisco', 65.0, '2023-02-01', '**********', true, NOW(), NOW())
ON CONFLICT ("userId") DO NOTHING;

-- Insert Resource Skills
INSERT INTO resource_skills ("resourceId", "skillId", "proficiencyLevel", "yearsOfExperience", certified, "createdAt", "updatedAt") VALUES
('res-1', 'skill-1', 5, 5, true, NOW(), NOW()),
('res-1', 'skill-3', 4, 3, false, NOW(), NOW()),
('res-1', 'skill-4', 4, 4, false, NOW(), NOW()),
('res-2', 'skill-6', 5, 6, true, NOW(), NOW())
ON CONFLICT ("resourceId", "skillId") DO NOTHING;

-- Insert Contracts
INSERT INTO contracts (id, title, description, "clientId", type, status, "startDate", "endDate", value, currency, terms, "createdAt", "updatedAt") VALUES
('contract-1', 'Web Application Development', 'Development of a modern web application with React and Node.js', 'client-1', 'TIME_AND_MATERIAL', 'ACTIVE', '2024-01-01', '2024-12-31', 500000, 'USD', 'Standard terms and conditions apply. Payment due within 30 days.', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert Projects
INSERT INTO projects (id, name, description, "contractId", "managerId", status, "startDate", "endDate", budget, "createdAt", "updatedAt") VALUES
('project-1', 'E-commerce Platform', 'Building a scalable e-commerce platform', 'contract-1', 'pm-1', 'ACTIVE', '2024-01-15', '2024-06-15', 250000, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert Project Resources
INSERT INTO project_resources (id, "projectId", "resourceId", "allocationPercent", "startDate", "createdAt", "updatedAt") VALUES
('pr-1', 'project-1', 'res-1', 100, '2024-01-15', NOW(), NOW()),
('pr-2', 'project-1', 'res-2', 50, '2024-01-15', NOW(), NOW())
ON CONFLICT ("projectId", "resourceId") DO NOTHING;

-- Insert Tasks
INSERT INTO tasks (id, title, description, "projectId", "assignedToId", status, priority, "estimatedHours", "actualHours", "startDate", "dueDate", "createdAt", "updatedAt") VALUES
('task-1', 'Setup Project Structure', 'Initialize the project with proper folder structure and dependencies', 'project-1', 'resource-1', 'COMPLETED', 'HIGH', 8, 6, '2024-01-15', '2024-01-16', NOW(), NOW()),
('task-2', 'Design User Interface', 'Create wireframes and mockups for the main user interface', 'project-1', 'resource-2', 'IN_PROGRESS', 'MEDIUM', 16, 8, '2024-01-16', '2024-01-20', NOW(), NOW()),
('task-3', 'Implement Authentication', 'Build user authentication and authorization system', 'project-1', 'resource-1', 'TODO', 'HIGH', 12, 0, '2024-01-20', '2024-01-25', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert Timesheets
INSERT INTO timesheets (id, "resourceId", "projectId", "weekStarting", "weekEnding", status, "totalHours", "submittedAt", "createdAt", "updatedAt") VALUES
('ts-1', 'res-1', 'project-1', '2024-01-15', '2024-01-21', 'APPROVED', 40, '2024-01-22 09:00:00', NOW(), NOW()),
('ts-2', 'res-2', 'project-1', '2024-01-15', '2024-01-21', 'SUBMITTED', 20, '2024-01-22 10:00:00', NOW(), NOW())
ON CONFLICT ("resourceId", "projectId", "weekStarting") DO NOTHING;

-- Insert Timesheet Entries
INSERT INTO timesheet_entries (id, "timesheetId", "taskId", date, hours, description, "createdAt", "updatedAt") VALUES
('te-1', 'ts-1', 'task-1', '2024-01-15', 8, 'Set up project repository and initial configuration', NOW(), NOW()),
('te-2', 'ts-1', 'task-1', '2024-01-16', 6, 'Completed project structure setup', NOW(), NOW()),
('te-3', 'ts-1', 'task-3', '2024-01-17', 8, 'Started working on authentication system', NOW(), NOW()),
('te-4', 'ts-1', 'task-3', '2024-01-18', 8, 'Continued authentication implementation', NOW(), NOW()),
('te-5', 'ts-1', 'task-3', '2024-01-19', 8, 'Testing authentication flows', NOW(), NOW()),
('te-6', 'ts-2', 'task-2', '2024-01-16', 8, 'Created initial wireframes', NOW(), NOW()),
('te-7', 'ts-2', 'task-2', '2024-01-17', 6, 'Designed user interface mockups', NOW(), NOW()),
('te-8', 'ts-2', 'task-2', '2024-01-18', 6, 'Refined UI designs based on feedback', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert Invoices
INSERT INTO invoices (id, "invoiceNumber", "projectId", "resourceId", "timesheetId", status, "issueDate", "dueDate", subtotal, tax, penalties, total, "createdAt", "updatedAt") VALUES
('inv-1', 'INV-2024-0001', 'project-1', 'res-1', 'ts-1', 'PAID', '2024-01-25', '2024-02-25', 3000, 240, 0, 3240, NOW(), NOW())
ON CONFLICT ("invoiceNumber") DO NOTHING;
