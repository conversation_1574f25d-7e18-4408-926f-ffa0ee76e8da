# Production Environment Configuration
# IMPORTANT: Copy this file to .env and update all values for production deployment

# Database Configuration
# Use a secure PostgreSQL connection string for production
DATABASE_URL="postgresql://username:password@host:port/database_name"

# Server Configuration
PORT=3003
NODE_ENV=production

# JWT Configuration - CRITICAL: Generate secure random secrets
# Use: openssl rand -hex 64
JWT_SECRET=REPLACE_WITH_SECURE_64_CHAR_HEX_STRING
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=REPLACE_WITH_DIFFERENT_SECURE_64_CHAR_HEX_STRING
JWT_REFRESH_EXPIRES_IN=30d

# CORS Configuration
# Set to your production frontend domain
CORS_ORIGIN=https://your-production-domain.com

# Email Configuration (Optional - for notifications)
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-secure-app-password

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Security Configuration
# Set to true in production for HTTPS
SECURE_COOKIES=true
TRUST_PROXY=true

# Monitoring Configuration (Optional)
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true

# Production Deployment Notes:
# 1. Generate secure JWT secrets using: openssl rand -hex 64
# 2. Use environment-specific database credentials
# 3. Set CORS_ORIGIN to your actual frontend domain
# 4. Configure proper SMTP settings for email notifications
# 5. Set up proper logging and monitoring
# 6. Use HTTPS in production (configure reverse proxy)
# 7. Set up database backups
# 8. Configure firewall rules
# 9. Set up SSL certificates
# 10. Configure monitoring and alerting
