#!/bin/bash

# Local Development Startup Script for Agentic Talent Pro
echo "🚀 Starting Agentic Talent Pro in Development Mode..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION'))" 2>/dev/null; then
    print_warning "Node.js version $REQUIRED_VERSION or higher is recommended. Current: $NODE_VERSION"
fi

# Check if PostgreSQL is running
print_step "Checking PostgreSQL connection..."
if ! nc -z localhost 5433 2>/dev/null; then
    print_error "PostgreSQL is not running on port 5433!"
    print_step "Please start PostgreSQL and ensure it's running on port 5433"
    print_step "Or update the DATABASE_URL in backend/.env to match your PostgreSQL configuration"
    exit 1
fi
print_success "PostgreSQL is running on port 5433"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_step "Installing root dependencies..."
    npm install
fi

if [ ! -d "backend/node_modules" ]; then
    print_step "Installing backend dependencies..."
    cd backend && npm install && cd ..
fi

if [ ! -d "frontend/node_modules" ]; then
    print_step "Installing frontend dependencies..."
    cd frontend && npm install && cd ..
fi

if [ ! -d "shared/node_modules" ]; then
    print_step "Installing shared dependencies..."
    cd shared && npm install && cd ..
fi

print_success "Dependencies installed"

# Build shared package
print_step "Building shared package..."
cd shared && npm run build && cd ..
print_success "Shared package built"

# Set up database
print_step "Setting up database..."
cd backend

# Generate Prisma client
npm run db:generate

# Run migrations
print_step "Running database migrations..."
npm run db:migrate

# Seed database (only in development)
print_step "Seeding database with development data..."
npm run db:seed

cd ..
print_success "Database setup completed"

# Start the application
print_step "Starting development servers..."
echo ""
print_success "🎉 Setup completed! Starting the application..."
echo ""
print_step "The application will be available at:"
echo "  📊 Frontend: http://localhost:3002"
echo "  🔗 Backend API: http://localhost:3003"
echo "  📚 API Documentation: http://localhost:3003/api/docs"
echo "  🏥 Health Check: http://localhost:3003/health"
echo ""
print_step "Demo Login Credentials:"
echo "  👤 Admin: <EMAIL> / admin123"
echo "  👤 Project Manager: <EMAIL> / pm123"
echo "  👤 HR Manager: <EMAIL> / hr123"
echo "  👤 Billing Manager: <EMAIL> / billing123"
echo "  👤 Resource: <EMAIL> / resource123"
echo "  👤 Client: <EMAIL> / client123"
echo ""
print_warning "Press Ctrl+C to stop the servers"
echo ""

# Start both frontend and backend
npm run dev
